# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

def Compile(graph: str = ..., config: str = ..., target_triple: str = ..., target_cpu: str = ..., target_features: str = ..., entry_point: str = ..., cpp_class: str = ..., out_function_object: str = ..., out_metadata_object: str = ..., out_header: str = ..., out_session_module: str = ..., mlir_components: str = ..., gen_name_to_index: bool = ..., gen_program_shape: bool = ...) -> None: ...
