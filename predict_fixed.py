# -*- coding: utf-8 -*-
# Plant Disease Classification Model - Fixed Version
import numpy as np
from PIL import Image
import warnings
import os

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Handle Keras 2.x to 3.x compatibility
from keras.models import Sequential
from keras.layers import Conv2D, MaxPooling2D, BatchNormalization, Dropout, Flatten, <PERSON><PERSON>

def create_model_architecture():
    """Recreate the model architecture to match the saved weights"""
    model = Sequential()
    
    # First Conv2D layer
    model.add(Conv2D(32, (3, 3), activation='relu', input_shape=(128, 128, 3), name='conv2d_26'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_26'))
    model.add(BatchNormalization(name='batch_normalization_26'))
    
    # Second Conv2D layer
    model.add(Conv2D(64, (3, 3), activation='relu', name='conv2d_27'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_27'))
    model.add(BatchNormalization(name='batch_normalization_27'))
    
    # Third Conv2D layer
    model.add(Conv2D(64, (3, 3), activation='relu', name='conv2d_28'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_28'))
    model.add(BatchNormalization(name='batch_normalization_28'))
    
    # Fourth Conv2D layer
    model.add(Conv2D(96, (3, 3), activation='relu', name='conv2d_29'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_29'))
    model.add(BatchNormalization(name='batch_normalization_29'))
    
    # Fifth Conv2D layer
    model.add(Conv2D(32, (3, 3), activation='relu', name='conv2d_30'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_30'))
    model.add(BatchNormalization(name='batch_normalization_30'))
    
    # Dropout and Dense layers
    model.add(Dropout(0.2, name='dropout_11'))
    model.add(Flatten(name='flatten_6'))
    model.add(Dense(128, activation='relu', name='dense_11'))
    model.add(Dropout(0.3, name='dropout_12'))
    model.add(Dense(25, activation='softmax', name='dense_12'))  # 25 classes
    
    return model

def load_and_preprocess_image(image_path, target_size=(128, 128)):
    """Load and preprocess image for prediction"""
    try:
        # Check if file exists
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        # Load image using PIL
        image = Image.open(image_path)
        
        # Convert to RGB if necessary (handles RGBA, grayscale, etc.)
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize image
        image = image.resize(target_size)
        
        # Convert to numpy array
        image_array = np.array(image)
        
        # Normalize pixel values to [0, 1] range
        image_array = image_array.astype('float32') / 255.0
        
        # Add batch dimension
        image_array = np.expand_dims(image_array, axis=0)
        
        return image_array
    
    except Exception as e:
        print(f"Error loading image: {e}")
        return None

def main():
    # Extended label list (25 classes to match model output)
    labels = [
        "Apple___Apple_scab", "Apple___Black_rot", "Apple___Cedar_apple_rust", "Apple___Healthy",
        "Grape___Black_rot", "Grape___Esca_(Black_Measles)", "Grape___Healthy",
        "Grape___Leaf_blight_(Isariopsis_Leaf_Spot)",
        "Potato___Early_blight", "Potato___Healthy", "Potato___Late_blight",
        "Tomato___Bacterial_spot", "Tomato___Early_blight", "Tomato___Healthy", 
        "Tomato___Late_blight", "Tomato___Leaf_Mold", "Tomato___Septoria_leaf_spot",
        "Tomato___Spider_mites_Two-spotted_spider_mite", "Tomato___Target_Spot",
        "Tomato___Tomato_Yellow_Leaf_Curl_Virus", "Tomato___Tomato_mosaic_virus",
        "Corn___Cercospora_leaf_spot", "Corn___Common_rust", "Corn___Healthy",
        "Corn___Northern_Leaf_Blight"
    ]
    
    print("Loading model...")
    
    # Create model architecture and load weights
    try:
        loaded_model = create_model_architecture()
        loaded_model.load_weights("model1.h5")
        print("✓ Model loaded successfully from disk")
    except Exception as e:
        print(f"✗ Error loading model: {e}")
        return
    
    # Try different possible image file extensions
    possible_images = [
        'Training_purpose/TYLCV1.png',
        'Training_purpose/TYLCV1.jpg', 
        'Training_purpose/TYLCV1.JPG',
        'Training_purpose/TYLCV1.jpeg'
    ]
    
    test_image_path = None
    for img_path in possible_images:
        if os.path.exists(img_path):
            test_image_path = img_path
            break
    
    if test_image_path is None:
        print("✗ No test image found. Available images in Training_purpose:")
        for file in os.listdir('Training_purpose'):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                print(f"  - {file}")
        return
    
    print(f"Using test image: {test_image_path}")
    
    # Load and preprocess the test image
    test_image = load_and_preprocess_image(test_image_path)
    if test_image is None:
        return
    
    print(f"✓ Image loaded successfully, shape: {test_image.shape}")
    
    # Make prediction
    try:
        result = loaded_model.predict(test_image, verbose=0)
        print(f"✓ Prediction completed, result shape: {result.shape}")
        
        # Get prediction results
        confidence = np.max(result)
        predicted_class_index = result.argmax()
        
        # Ensure the predicted class index is within our label range
        if predicted_class_index < len(labels):
            predicted_label = labels[predicted_class_index]
        else:
            predicted_label = f"Unknown_Class_{predicted_class_index}"
        
        # Display results
        print("\n" + "="*50)
        print("PREDICTION RESULTS")
        print("="*50)
        print(f"Predicted Class: {predicted_label}")
        print(f"Confidence: {confidence:.4f} ({confidence*100:.2f}%)")
        print(f"Class Index: {predicted_class_index}")
        print("\nTop 3 predictions:")
        
        # Get top 3 predictions
        top_3_indices = np.argsort(result[0])[-3:][::-1]
        for i, idx in enumerate(top_3_indices):
            if idx < len(labels):
                label_name = labels[idx]
            else:
                label_name = f"Unknown_Class_{idx}"
            print(f"{i+1}. {label_name}: {result[0][idx]:.4f} ({result[0][idx]*100:.2f}%)")
        
    except Exception as e:
        print(f"✗ Error during prediction: {e}")
        return

if __name__ == "__main__":
    main()
