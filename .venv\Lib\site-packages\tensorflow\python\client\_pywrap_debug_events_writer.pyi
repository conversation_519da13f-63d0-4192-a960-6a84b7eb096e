# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

def Close(arg0: str) -> None: ...
def FlushExecutionFiles(arg0: str) -> None: ...
def FlushNonExecutionFiles(arg0: str) -> None: ...
def Init(arg0: str, arg1: str, arg2: int) -> None: ...
def RegisterDeviceAndGetId(arg0: str, arg1: str) -> int: ...
def WriteDebuggedGraph(arg0: str, arg1: object) -> None: ...
def WriteExecution(arg0: str, arg1: object) -> None: ...
def WriteGraphExecutionTrace(arg0: str, arg1: object) -> None: ...
def WriteGraphOpCreation(arg0: str, arg1: object) -> None: ...
def WriteSourceFile(arg0: str, arg1: object) -> None: ...
def WriteStackFrameWithId(arg0: str, arg1: object) -> None: ...
