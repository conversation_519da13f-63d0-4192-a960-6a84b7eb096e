# -*- coding: utf-8 -*-
# Plant Disease Classification - Simple Fix
import numpy as np
from PIL import Image
import warnings
import os

# Suppress warnings
warnings.filterwarnings('ignore')

# Use modern Keras imports instead of deprecated ones
from keras.models import Sequential
from keras.layers import Conv2D, MaxPooling2D, BatchNormalization, Dropout, Flatten, <PERSON><PERSON>

def create_model_architecture():
    """Recreate the model architecture to match the saved weights"""
    model = Sequential()
    
    # Architecture matching the saved model
    model.add(Conv2D(32, (3, 3), activation='relu', input_shape=(128, 128, 3), name='conv2d_26'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_26'))
    model.add(BatchNormalization(name='batch_normalization_26'))
    model.add(Conv2D(64, (3, 3), activation='relu', name='conv2d_27'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_27'))
    model.add(BatchNormalization(name='batch_normalization_27'))
    model.add(Conv2D(64, (3, 3), activation='relu', name='conv2d_28'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_28'))
    model.add(BatchNormalization(name='batch_normalization_28'))
    model.add(Conv2D(96, (3, 3), activation='relu', name='conv2d_29'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_29'))
    model.add(BatchNormalization(name='batch_normalization_29'))
    model.add(Conv2D(32, (3, 3), activation='relu', name='conv2d_30'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_30'))
    model.add(BatchNormalization(name='batch_normalization_30'))
    model.add(Dropout(0.2, name='dropout_11'))
    model.add(Flatten(name='flatten_6'))
    model.add(Dense(128, activation='relu', name='dense_11'))
    model.add(Dropout(0.3, name='dropout_12'))
    model.add(Dense(25, activation='softmax', name='dense_12'))
    
    return model

# Create model and load weights
try:
    loaded_model = create_model_architecture()
    loaded_model.load_weights("model1.h5")
    print("Loaded model from disk")
except Exception as e:
    print(f"Error loading model: {e}")
    exit(1)

# Original labels (15 classes) - keeping your original list
label = ["Apple___Apple_scab","Apple___Black_rot","Apple___Cedar_apple_rust","Apple___Healthy",
         "Grape___Black_rot","Grape___Esca_(Black_Measles)","Grape___Healthy",
         "Grape___Leaf_blight_(Isariopsis_Leaf_Spot)",
         "Potato___Early_blight","Potato___Healthy","Tomato___Bacterial_spot",
         "Tomato___Healthy","Tomato___Leaf_Mold",
         "Tomato___Tomato_Yellow_Leaf_Curl_Virus","Tomato___Tomato_mosaic_virus"]

# Fix 1: Use correct file extension (.png instead of .JPG)
# Fix 2: Use PIL instead of deprecated keras.preprocessing.image
try:
    # Try different possible file extensions
    image_files = ['Training_purpose/TYLCV1.png', 'Training_purpose/TYLCV1.JPG', 'Training_purpose/TYLCV1.jpg']
    test_image_path = None
    
    for img_path in image_files:
        if os.path.exists(img_path):
            test_image_path = img_path
            break
    
    if test_image_path is None:
        raise FileNotFoundError("Could not find TYLCV1 image file")
    
    print(f"Loading image: {test_image_path}")
    
    # Load and preprocess image using PIL
    test_image = Image.open(test_image_path).resize((128, 128))
    
    # Convert RGBA to RGB if necessary
    if test_image.mode != 'RGB':
        test_image = test_image.convert('RGB')
    
    # Convert to numpy array and normalize
    test_image = np.array(test_image)
    test_image = test_image.astype('float32') / 255.0  # Normalize to [0,1]
    test_image = np.expand_dims(test_image, axis=0)
    
    print(f"Image shape: {test_image.shape}")
    
except Exception as e:
    print(f"Error loading image: {e}")
    exit(1)

# Make prediction
try:
    result = loaded_model.predict(test_image)
    print("Raw prediction result:")
    print(result)
    
    # Get results
    fresult = np.max(result)
    predicted_class_index = result.argmax()
    
    # Fix 3: Handle case where predicted class is outside our label range
    if predicted_class_index < len(label):
        label2 = label[predicted_class_index]
    else:
        label2 = f"Unknown_Class_{predicted_class_index}"
    
    print(f"\nPredicted class: {label2}")
    print(f"Confidence: {fresult:.4f}")
    print(f"Class index: {predicted_class_index}")
    
except Exception as e:
    print(f"Error during prediction: {e}")
    exit(1)
