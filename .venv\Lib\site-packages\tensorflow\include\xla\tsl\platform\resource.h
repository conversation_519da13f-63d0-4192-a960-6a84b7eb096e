/* Copyright 2020 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef XLA_TSL_PLATFORM_RESOURCE_H_
#define XLA_TSL_PLATFORM_RESOURCE_H_

#include <memory>

#include "tsl/platform/stringpiece.h"

namespace tsl {

// ResourceTagger objects should only be allocated on the stack.
class ResourceTagger {
 public:
  ResourceTagger(absl::string_view key, absl::string_view value);
  ~ResourceTagger();

  // Do not allow copying or moving ResourceTagger
  ResourceTagger(const ResourceTagger&) = delete;
  ResourceTagger(ResourceTagger&&) = delete;
  ResourceTagger& operator=(const ResourceTagger&) = delete;
  ResourceTagger& operator=(ResourceTagger&&) = delete;

 private:
  class ResourceTaggerImpl;
  const std::unique_ptr<ResourceTaggerImpl> impl_;
};

}  // namespace tsl

#endif  // XLA_TSL_PLATFORM_RESOURCE_H_
