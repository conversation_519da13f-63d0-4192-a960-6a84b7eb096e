# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

import typing
from typing import Iterator, overload

class GraphDebugInfoBuilder:
    def __init__(self) -> None: ...
    def AccumulateStackTrace(self, function: str, op: str, trace) -> None: ...
    def AppendGraphDebugInfo(self, prefix: str, debug_info: bytes) -> None: ...
    def Build(self) -> bytes: ...

class PyBindFileSet:
    def __init__(self) -> None: ...
    def update_to(self, arg0: set) -> None: ...

class PyBindSourceMap:
    def __init__(self) -> None: ...
    def update_to(self, arg0: tuple) -> None: ...

class StackFrame:
    def __init__(self, *args, **kwargs) -> None: ...
    def __eq__(self, arg0: StackFrame) -> bool: ...
    def __getitem__(self, arg0: object) -> object: ...
    def __hash__(self) -> int: ...
    def __iter__(self) -> Iterator: ...
    def __len__(self) -> int: ...
    def __ne__(self, arg0: StackFrame) -> bool: ...
    @property
    def filename(self) -> str: ...
    @property
    def line(self) -> str: ...
    @property
    def lineno(self) -> int: ...
    @property
    def name(self) -> str: ...

class StackTrace:
    def __init__(self, *args, **kwargs) -> None: ...
    def get_user_frames(self) -> StackTrace: ...
    def last_user_frame(self) -> StackFrame: ...
    def __eq__(self, arg0: StackTrace) -> bool: ...
    @overload
    def __getitem__(self, arg0: int) -> StackFrame: ...
    @overload
    def __getitem__(self, arg0: slice) -> StackTrace: ...
    def __iter__(self) -> typing.Iterator[StackFrame]: ...
    def __hash__(self) -> int: ...
    def __len__(self) -> int: ...

def LoadTracesFromDebugInfo(debug_info_proto: bytes) -> dict[str, StackTrace]: ...
def extract_stack(source_map: PyBindSourceMap, file_set: PyBindFileSet, stacklevel: int = ...) -> StackTrace: ...
