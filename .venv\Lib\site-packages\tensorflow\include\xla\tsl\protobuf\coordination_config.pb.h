// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/tsl/protobuf/coordination_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto;
namespace tensorflow {
class CoordinatedJob;
struct CoordinatedJobDefaultTypeInternal;
extern CoordinatedJobDefaultTypeInternal _CoordinatedJob_default_instance_;
class CoordinationServiceConfig;
struct CoordinationServiceConfigDefaultTypeInternal;
extern CoordinationServiceConfigDefaultTypeInternal _CoordinationServiceConfig_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CoordinatedJob* Arena::CreateMaybeMessage<::tensorflow::CoordinatedJob>(Arena*);
template<> ::tensorflow::CoordinationServiceConfig* Arena::CreateMaybeMessage<::tensorflow::CoordinationServiceConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CoordinatedJob final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CoordinatedJob) */ {
 public:
  inline CoordinatedJob() : CoordinatedJob(nullptr) {}
  ~CoordinatedJob() override;
  explicit PROTOBUF_CONSTEXPR CoordinatedJob(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CoordinatedJob(const CoordinatedJob& from);
  CoordinatedJob(CoordinatedJob&& from) noexcept
    : CoordinatedJob() {
    *this = ::std::move(from);
  }

  inline CoordinatedJob& operator=(const CoordinatedJob& from) {
    CopyFrom(from);
    return *this;
  }
  inline CoordinatedJob& operator=(CoordinatedJob&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CoordinatedJob& default_instance() {
    return *internal_default_instance();
  }
  static inline const CoordinatedJob* internal_default_instance() {
    return reinterpret_cast<const CoordinatedJob*>(
               &_CoordinatedJob_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CoordinatedJob& a, CoordinatedJob& b) {
    a.Swap(&b);
  }
  inline void Swap(CoordinatedJob* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CoordinatedJob* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CoordinatedJob* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CoordinatedJob>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CoordinatedJob& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CoordinatedJob& from) {
    CoordinatedJob::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CoordinatedJob* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CoordinatedJob";
  }
  protected:
  explicit CoordinatedJob(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kNumTasksFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int32 num_tasks = 2;
  void clear_num_tasks();
  int32_t num_tasks() const;
  void set_num_tasks(int32_t value);
  private:
  int32_t _internal_num_tasks() const;
  void _internal_set_num_tasks(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CoordinatedJob)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int32_t num_tasks_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class CoordinationServiceConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CoordinationServiceConfig) */ {
 public:
  inline CoordinationServiceConfig() : CoordinationServiceConfig(nullptr) {}
  ~CoordinationServiceConfig() override;
  explicit PROTOBUF_CONSTEXPR CoordinationServiceConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CoordinationServiceConfig(const CoordinationServiceConfig& from);
  CoordinationServiceConfig(CoordinationServiceConfig&& from) noexcept
    : CoordinationServiceConfig() {
    *this = ::std::move(from);
  }

  inline CoordinationServiceConfig& operator=(const CoordinationServiceConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline CoordinationServiceConfig& operator=(CoordinationServiceConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CoordinationServiceConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const CoordinationServiceConfig* internal_default_instance() {
    return reinterpret_cast<const CoordinationServiceConfig*>(
               &_CoordinationServiceConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CoordinationServiceConfig& a, CoordinationServiceConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(CoordinationServiceConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CoordinationServiceConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CoordinationServiceConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CoordinationServiceConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CoordinationServiceConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CoordinationServiceConfig& from) {
    CoordinationServiceConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CoordinationServiceConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CoordinationServiceConfig";
  }
  protected:
  explicit CoordinationServiceConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecoverableJobsFieldNumber = 9,
    kCoordinatedJobListFieldNumber = 10,
    kServiceTypeFieldNumber = 1,
    kServiceLeaderFieldNumber = 2,
    kClusterRegisterTimeoutInMsFieldNumber = 4,
    kHeartbeatTimeoutInMsFieldNumber = 5,
    kShutdownBarrierTimeoutInMsFieldNumber = 7,
    kEnableHealthCheckFieldNumber = 3,
    kClusterRegisterWithBarrierFieldNumber = 14,
    kAgentDestructionWithoutShutdownFieldNumber = 8,
    kAllowNewIncarnationToReconnectFieldNumber = 11,
    kForceDisableFieldNumber = 12,
    kPollForErrorFromServiceAtStartupFieldNumber = 13,
  };
  // repeated string recoverable_jobs = 9;
  int recoverable_jobs_size() const;
  private:
  int _internal_recoverable_jobs_size() const;
  public:
  void clear_recoverable_jobs();
  const std::string& recoverable_jobs(int index) const;
  std::string* mutable_recoverable_jobs(int index);
  void set_recoverable_jobs(int index, const std::string& value);
  void set_recoverable_jobs(int index, std::string&& value);
  void set_recoverable_jobs(int index, const char* value);
  void set_recoverable_jobs(int index, const char* value, size_t size);
  std::string* add_recoverable_jobs();
  void add_recoverable_jobs(const std::string& value);
  void add_recoverable_jobs(std::string&& value);
  void add_recoverable_jobs(const char* value);
  void add_recoverable_jobs(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& recoverable_jobs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_recoverable_jobs();
  private:
  const std::string& _internal_recoverable_jobs(int index) const;
  std::string* _internal_add_recoverable_jobs();
  public:

  // repeated .tensorflow.CoordinatedJob coordinated_job_list = 10;
  int coordinated_job_list_size() const;
  private:
  int _internal_coordinated_job_list_size() const;
  public:
  void clear_coordinated_job_list();
  ::tensorflow::CoordinatedJob* mutable_coordinated_job_list(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedJob >*
      mutable_coordinated_job_list();
  private:
  const ::tensorflow::CoordinatedJob& _internal_coordinated_job_list(int index) const;
  ::tensorflow::CoordinatedJob* _internal_add_coordinated_job_list();
  public:
  const ::tensorflow::CoordinatedJob& coordinated_job_list(int index) const;
  ::tensorflow::CoordinatedJob* add_coordinated_job_list();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedJob >&
      coordinated_job_list() const;

  // string service_type = 1;
  void clear_service_type();
  const std::string& service_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_service_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_service_type();
  PROTOBUF_NODISCARD std::string* release_service_type();
  void set_allocated_service_type(std::string* service_type);
  private:
  const std::string& _internal_service_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_service_type(const std::string& value);
  std::string* _internal_mutable_service_type();
  public:

  // string service_leader = 2;
  void clear_service_leader();
  const std::string& service_leader() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_service_leader(ArgT0&& arg0, ArgT... args);
  std::string* mutable_service_leader();
  PROTOBUF_NODISCARD std::string* release_service_leader();
  void set_allocated_service_leader(std::string* service_leader);
  private:
  const std::string& _internal_service_leader() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_service_leader(const std::string& value);
  std::string* _internal_mutable_service_leader();
  public:

  // int64 cluster_register_timeout_in_ms = 4;
  void clear_cluster_register_timeout_in_ms();
  int64_t cluster_register_timeout_in_ms() const;
  void set_cluster_register_timeout_in_ms(int64_t value);
  private:
  int64_t _internal_cluster_register_timeout_in_ms() const;
  void _internal_set_cluster_register_timeout_in_ms(int64_t value);
  public:

  // int64 heartbeat_timeout_in_ms = 5;
  void clear_heartbeat_timeout_in_ms();
  int64_t heartbeat_timeout_in_ms() const;
  void set_heartbeat_timeout_in_ms(int64_t value);
  private:
  int64_t _internal_heartbeat_timeout_in_ms() const;
  void _internal_set_heartbeat_timeout_in_ms(int64_t value);
  public:

  // int64 shutdown_barrier_timeout_in_ms = 7;
  void clear_shutdown_barrier_timeout_in_ms();
  int64_t shutdown_barrier_timeout_in_ms() const;
  void set_shutdown_barrier_timeout_in_ms(int64_t value);
  private:
  int64_t _internal_shutdown_barrier_timeout_in_ms() const;
  void _internal_set_shutdown_barrier_timeout_in_ms(int64_t value);
  public:

  // bool enable_health_check = 3;
  void clear_enable_health_check();
  bool enable_health_check() const;
  void set_enable_health_check(bool value);
  private:
  bool _internal_enable_health_check() const;
  void _internal_set_enable_health_check(bool value);
  public:

  // bool cluster_register_with_barrier = 14;
  void clear_cluster_register_with_barrier();
  bool cluster_register_with_barrier() const;
  void set_cluster_register_with_barrier(bool value);
  private:
  bool _internal_cluster_register_with_barrier() const;
  void _internal_set_cluster_register_with_barrier(bool value);
  public:

  // bool agent_destruction_without_shutdown = 8;
  void clear_agent_destruction_without_shutdown();
  bool agent_destruction_without_shutdown() const;
  void set_agent_destruction_without_shutdown(bool value);
  private:
  bool _internal_agent_destruction_without_shutdown() const;
  void _internal_set_agent_destruction_without_shutdown(bool value);
  public:

  // bool allow_new_incarnation_to_reconnect = 11;
  void clear_allow_new_incarnation_to_reconnect();
  bool allow_new_incarnation_to_reconnect() const;
  void set_allow_new_incarnation_to_reconnect(bool value);
  private:
  bool _internal_allow_new_incarnation_to_reconnect() const;
  void _internal_set_allow_new_incarnation_to_reconnect(bool value);
  public:

  // bool force_disable = 12;
  void clear_force_disable();
  bool force_disable() const;
  void set_force_disable(bool value);
  private:
  bool _internal_force_disable() const;
  void _internal_set_force_disable(bool value);
  public:

  // bool poll_for_error_from_service_at_startup = 13;
  void clear_poll_for_error_from_service_at_startup();
  bool poll_for_error_from_service_at_startup() const;
  void set_poll_for_error_from_service_at_startup(bool value);
  private:
  bool _internal_poll_for_error_from_service_at_startup() const;
  void _internal_set_poll_for_error_from_service_at_startup(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CoordinationServiceConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> recoverable_jobs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedJob > coordinated_job_list_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr service_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr service_leader_;
    int64_t cluster_register_timeout_in_ms_;
    int64_t heartbeat_timeout_in_ms_;
    int64_t shutdown_barrier_timeout_in_ms_;
    bool enable_health_check_;
    bool cluster_register_with_barrier_;
    bool agent_destruction_without_shutdown_;
    bool allow_new_incarnation_to_reconnect_;
    bool force_disable_;
    bool poll_for_error_from_service_at_startup_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CoordinatedJob

// string name = 1;
inline void CoordinatedJob::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& CoordinatedJob::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedJob.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CoordinatedJob::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedJob.name)
}
inline std::string* CoordinatedJob::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinatedJob.name)
  return _s;
}
inline const std::string& CoordinatedJob::_internal_name() const {
  return _impl_.name_.Get();
}
inline void CoordinatedJob::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* CoordinatedJob::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* CoordinatedJob::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinatedJob.name)
  return _impl_.name_.Release();
}
inline void CoordinatedJob::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinatedJob.name)
}

// int32 num_tasks = 2;
inline void CoordinatedJob::clear_num_tasks() {
  _impl_.num_tasks_ = 0;
}
inline int32_t CoordinatedJob::_internal_num_tasks() const {
  return _impl_.num_tasks_;
}
inline int32_t CoordinatedJob::num_tasks() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedJob.num_tasks)
  return _internal_num_tasks();
}
inline void CoordinatedJob::_internal_set_num_tasks(int32_t value) {
  
  _impl_.num_tasks_ = value;
}
inline void CoordinatedJob::set_num_tasks(int32_t value) {
  _internal_set_num_tasks(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedJob.num_tasks)
}

// -------------------------------------------------------------------

// CoordinationServiceConfig

// string service_type = 1;
inline void CoordinationServiceConfig::clear_service_type() {
  _impl_.service_type_.ClearToEmpty();
}
inline const std::string& CoordinationServiceConfig::service_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.service_type)
  return _internal_service_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CoordinationServiceConfig::set_service_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.service_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.service_type)
}
inline std::string* CoordinationServiceConfig::mutable_service_type() {
  std::string* _s = _internal_mutable_service_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinationServiceConfig.service_type)
  return _s;
}
inline const std::string& CoordinationServiceConfig::_internal_service_type() const {
  return _impl_.service_type_.Get();
}
inline void CoordinationServiceConfig::_internal_set_service_type(const std::string& value) {
  
  _impl_.service_type_.Set(value, GetArenaForAllocation());
}
inline std::string* CoordinationServiceConfig::_internal_mutable_service_type() {
  
  return _impl_.service_type_.Mutable(GetArenaForAllocation());
}
inline std::string* CoordinationServiceConfig::release_service_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinationServiceConfig.service_type)
  return _impl_.service_type_.Release();
}
inline void CoordinationServiceConfig::set_allocated_service_type(std::string* service_type) {
  if (service_type != nullptr) {
    
  } else {
    
  }
  _impl_.service_type_.SetAllocated(service_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.service_type_.IsDefault()) {
    _impl_.service_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinationServiceConfig.service_type)
}

// string service_leader = 2;
inline void CoordinationServiceConfig::clear_service_leader() {
  _impl_.service_leader_.ClearToEmpty();
}
inline const std::string& CoordinationServiceConfig::service_leader() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.service_leader)
  return _internal_service_leader();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CoordinationServiceConfig::set_service_leader(ArgT0&& arg0, ArgT... args) {
 
 _impl_.service_leader_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.service_leader)
}
inline std::string* CoordinationServiceConfig::mutable_service_leader() {
  std::string* _s = _internal_mutable_service_leader();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinationServiceConfig.service_leader)
  return _s;
}
inline const std::string& CoordinationServiceConfig::_internal_service_leader() const {
  return _impl_.service_leader_.Get();
}
inline void CoordinationServiceConfig::_internal_set_service_leader(const std::string& value) {
  
  _impl_.service_leader_.Set(value, GetArenaForAllocation());
}
inline std::string* CoordinationServiceConfig::_internal_mutable_service_leader() {
  
  return _impl_.service_leader_.Mutable(GetArenaForAllocation());
}
inline std::string* CoordinationServiceConfig::release_service_leader() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinationServiceConfig.service_leader)
  return _impl_.service_leader_.Release();
}
inline void CoordinationServiceConfig::set_allocated_service_leader(std::string* service_leader) {
  if (service_leader != nullptr) {
    
  } else {
    
  }
  _impl_.service_leader_.SetAllocated(service_leader, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.service_leader_.IsDefault()) {
    _impl_.service_leader_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinationServiceConfig.service_leader)
}

// bool enable_health_check = 3;
inline void CoordinationServiceConfig::clear_enable_health_check() {
  _impl_.enable_health_check_ = false;
}
inline bool CoordinationServiceConfig::_internal_enable_health_check() const {
  return _impl_.enable_health_check_;
}
inline bool CoordinationServiceConfig::enable_health_check() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.enable_health_check)
  return _internal_enable_health_check();
}
inline void CoordinationServiceConfig::_internal_set_enable_health_check(bool value) {
  
  _impl_.enable_health_check_ = value;
}
inline void CoordinationServiceConfig::set_enable_health_check(bool value) {
  _internal_set_enable_health_check(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.enable_health_check)
}

// int64 cluster_register_timeout_in_ms = 4;
inline void CoordinationServiceConfig::clear_cluster_register_timeout_in_ms() {
  _impl_.cluster_register_timeout_in_ms_ = int64_t{0};
}
inline int64_t CoordinationServiceConfig::_internal_cluster_register_timeout_in_ms() const {
  return _impl_.cluster_register_timeout_in_ms_;
}
inline int64_t CoordinationServiceConfig::cluster_register_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.cluster_register_timeout_in_ms)
  return _internal_cluster_register_timeout_in_ms();
}
inline void CoordinationServiceConfig::_internal_set_cluster_register_timeout_in_ms(int64_t value) {
  
  _impl_.cluster_register_timeout_in_ms_ = value;
}
inline void CoordinationServiceConfig::set_cluster_register_timeout_in_ms(int64_t value) {
  _internal_set_cluster_register_timeout_in_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.cluster_register_timeout_in_ms)
}

// bool cluster_register_with_barrier = 14;
inline void CoordinationServiceConfig::clear_cluster_register_with_barrier() {
  _impl_.cluster_register_with_barrier_ = false;
}
inline bool CoordinationServiceConfig::_internal_cluster_register_with_barrier() const {
  return _impl_.cluster_register_with_barrier_;
}
inline bool CoordinationServiceConfig::cluster_register_with_barrier() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.cluster_register_with_barrier)
  return _internal_cluster_register_with_barrier();
}
inline void CoordinationServiceConfig::_internal_set_cluster_register_with_barrier(bool value) {
  
  _impl_.cluster_register_with_barrier_ = value;
}
inline void CoordinationServiceConfig::set_cluster_register_with_barrier(bool value) {
  _internal_set_cluster_register_with_barrier(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.cluster_register_with_barrier)
}

// int64 heartbeat_timeout_in_ms = 5;
inline void CoordinationServiceConfig::clear_heartbeat_timeout_in_ms() {
  _impl_.heartbeat_timeout_in_ms_ = int64_t{0};
}
inline int64_t CoordinationServiceConfig::_internal_heartbeat_timeout_in_ms() const {
  return _impl_.heartbeat_timeout_in_ms_;
}
inline int64_t CoordinationServiceConfig::heartbeat_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.heartbeat_timeout_in_ms)
  return _internal_heartbeat_timeout_in_ms();
}
inline void CoordinationServiceConfig::_internal_set_heartbeat_timeout_in_ms(int64_t value) {
  
  _impl_.heartbeat_timeout_in_ms_ = value;
}
inline void CoordinationServiceConfig::set_heartbeat_timeout_in_ms(int64_t value) {
  _internal_set_heartbeat_timeout_in_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.heartbeat_timeout_in_ms)
}

// repeated .tensorflow.CoordinatedJob coordinated_job_list = 10;
inline int CoordinationServiceConfig::_internal_coordinated_job_list_size() const {
  return _impl_.coordinated_job_list_.size();
}
inline int CoordinationServiceConfig::coordinated_job_list_size() const {
  return _internal_coordinated_job_list_size();
}
inline void CoordinationServiceConfig::clear_coordinated_job_list() {
  _impl_.coordinated_job_list_.Clear();
}
inline ::tensorflow::CoordinatedJob* CoordinationServiceConfig::mutable_coordinated_job_list(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinationServiceConfig.coordinated_job_list)
  return _impl_.coordinated_job_list_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedJob >*
CoordinationServiceConfig::mutable_coordinated_job_list() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CoordinationServiceConfig.coordinated_job_list)
  return &_impl_.coordinated_job_list_;
}
inline const ::tensorflow::CoordinatedJob& CoordinationServiceConfig::_internal_coordinated_job_list(int index) const {
  return _impl_.coordinated_job_list_.Get(index);
}
inline const ::tensorflow::CoordinatedJob& CoordinationServiceConfig::coordinated_job_list(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.coordinated_job_list)
  return _internal_coordinated_job_list(index);
}
inline ::tensorflow::CoordinatedJob* CoordinationServiceConfig::_internal_add_coordinated_job_list() {
  return _impl_.coordinated_job_list_.Add();
}
inline ::tensorflow::CoordinatedJob* CoordinationServiceConfig::add_coordinated_job_list() {
  ::tensorflow::CoordinatedJob* _add = _internal_add_coordinated_job_list();
  // @@protoc_insertion_point(field_add:tensorflow.CoordinationServiceConfig.coordinated_job_list)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedJob >&
CoordinationServiceConfig::coordinated_job_list() const {
  // @@protoc_insertion_point(field_list:tensorflow.CoordinationServiceConfig.coordinated_job_list)
  return _impl_.coordinated_job_list_;
}

// int64 shutdown_barrier_timeout_in_ms = 7;
inline void CoordinationServiceConfig::clear_shutdown_barrier_timeout_in_ms() {
  _impl_.shutdown_barrier_timeout_in_ms_ = int64_t{0};
}
inline int64_t CoordinationServiceConfig::_internal_shutdown_barrier_timeout_in_ms() const {
  return _impl_.shutdown_barrier_timeout_in_ms_;
}
inline int64_t CoordinationServiceConfig::shutdown_barrier_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.shutdown_barrier_timeout_in_ms)
  return _internal_shutdown_barrier_timeout_in_ms();
}
inline void CoordinationServiceConfig::_internal_set_shutdown_barrier_timeout_in_ms(int64_t value) {
  
  _impl_.shutdown_barrier_timeout_in_ms_ = value;
}
inline void CoordinationServiceConfig::set_shutdown_barrier_timeout_in_ms(int64_t value) {
  _internal_set_shutdown_barrier_timeout_in_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.shutdown_barrier_timeout_in_ms)
}

// bool agent_destruction_without_shutdown = 8;
inline void CoordinationServiceConfig::clear_agent_destruction_without_shutdown() {
  _impl_.agent_destruction_without_shutdown_ = false;
}
inline bool CoordinationServiceConfig::_internal_agent_destruction_without_shutdown() const {
  return _impl_.agent_destruction_without_shutdown_;
}
inline bool CoordinationServiceConfig::agent_destruction_without_shutdown() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.agent_destruction_without_shutdown)
  return _internal_agent_destruction_without_shutdown();
}
inline void CoordinationServiceConfig::_internal_set_agent_destruction_without_shutdown(bool value) {
  
  _impl_.agent_destruction_without_shutdown_ = value;
}
inline void CoordinationServiceConfig::set_agent_destruction_without_shutdown(bool value) {
  _internal_set_agent_destruction_without_shutdown(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.agent_destruction_without_shutdown)
}

// repeated string recoverable_jobs = 9;
inline int CoordinationServiceConfig::_internal_recoverable_jobs_size() const {
  return _impl_.recoverable_jobs_.size();
}
inline int CoordinationServiceConfig::recoverable_jobs_size() const {
  return _internal_recoverable_jobs_size();
}
inline void CoordinationServiceConfig::clear_recoverable_jobs() {
  _impl_.recoverable_jobs_.Clear();
}
inline std::string* CoordinationServiceConfig::add_recoverable_jobs() {
  std::string* _s = _internal_add_recoverable_jobs();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CoordinationServiceConfig.recoverable_jobs)
  return _s;
}
inline const std::string& CoordinationServiceConfig::_internal_recoverable_jobs(int index) const {
  return _impl_.recoverable_jobs_.Get(index);
}
inline const std::string& CoordinationServiceConfig::recoverable_jobs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.recoverable_jobs)
  return _internal_recoverable_jobs(index);
}
inline std::string* CoordinationServiceConfig::mutable_recoverable_jobs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinationServiceConfig.recoverable_jobs)
  return _impl_.recoverable_jobs_.Mutable(index);
}
inline void CoordinationServiceConfig::set_recoverable_jobs(int index, const std::string& value) {
  _impl_.recoverable_jobs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline void CoordinationServiceConfig::set_recoverable_jobs(int index, std::string&& value) {
  _impl_.recoverable_jobs_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline void CoordinationServiceConfig::set_recoverable_jobs(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.recoverable_jobs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline void CoordinationServiceConfig::set_recoverable_jobs(int index, const char* value, size_t size) {
  _impl_.recoverable_jobs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline std::string* CoordinationServiceConfig::_internal_add_recoverable_jobs() {
  return _impl_.recoverable_jobs_.Add();
}
inline void CoordinationServiceConfig::add_recoverable_jobs(const std::string& value) {
  _impl_.recoverable_jobs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline void CoordinationServiceConfig::add_recoverable_jobs(std::string&& value) {
  _impl_.recoverable_jobs_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline void CoordinationServiceConfig::add_recoverable_jobs(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.recoverable_jobs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline void CoordinationServiceConfig::add_recoverable_jobs(const char* value, size_t size) {
  _impl_.recoverable_jobs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CoordinationServiceConfig.recoverable_jobs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CoordinationServiceConfig::recoverable_jobs() const {
  // @@protoc_insertion_point(field_list:tensorflow.CoordinationServiceConfig.recoverable_jobs)
  return _impl_.recoverable_jobs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CoordinationServiceConfig::mutable_recoverable_jobs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CoordinationServiceConfig.recoverable_jobs)
  return &_impl_.recoverable_jobs_;
}

// bool allow_new_incarnation_to_reconnect = 11;
inline void CoordinationServiceConfig::clear_allow_new_incarnation_to_reconnect() {
  _impl_.allow_new_incarnation_to_reconnect_ = false;
}
inline bool CoordinationServiceConfig::_internal_allow_new_incarnation_to_reconnect() const {
  return _impl_.allow_new_incarnation_to_reconnect_;
}
inline bool CoordinationServiceConfig::allow_new_incarnation_to_reconnect() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.allow_new_incarnation_to_reconnect)
  return _internal_allow_new_incarnation_to_reconnect();
}
inline void CoordinationServiceConfig::_internal_set_allow_new_incarnation_to_reconnect(bool value) {
  
  _impl_.allow_new_incarnation_to_reconnect_ = value;
}
inline void CoordinationServiceConfig::set_allow_new_incarnation_to_reconnect(bool value) {
  _internal_set_allow_new_incarnation_to_reconnect(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.allow_new_incarnation_to_reconnect)
}

// bool force_disable = 12;
inline void CoordinationServiceConfig::clear_force_disable() {
  _impl_.force_disable_ = false;
}
inline bool CoordinationServiceConfig::_internal_force_disable() const {
  return _impl_.force_disable_;
}
inline bool CoordinationServiceConfig::force_disable() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.force_disable)
  return _internal_force_disable();
}
inline void CoordinationServiceConfig::_internal_set_force_disable(bool value) {
  
  _impl_.force_disable_ = value;
}
inline void CoordinationServiceConfig::set_force_disable(bool value) {
  _internal_set_force_disable(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.force_disable)
}

// bool poll_for_error_from_service_at_startup = 13;
inline void CoordinationServiceConfig::clear_poll_for_error_from_service_at_startup() {
  _impl_.poll_for_error_from_service_at_startup_ = false;
}
inline bool CoordinationServiceConfig::_internal_poll_for_error_from_service_at_startup() const {
  return _impl_.poll_for_error_from_service_at_startup_;
}
inline bool CoordinationServiceConfig::poll_for_error_from_service_at_startup() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceConfig.poll_for_error_from_service_at_startup)
  return _internal_poll_for_error_from_service_at_startup();
}
inline void CoordinationServiceConfig::_internal_set_poll_for_error_from_service_at_startup(bool value) {
  
  _impl_.poll_for_error_from_service_at_startup_ = value;
}
inline void CoordinationServiceConfig::set_poll_for_error_from_service_at_startup(bool value) {
  _internal_set_poll_for_error_from_service_at_startup(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceConfig.poll_for_error_from_service_at_startup)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fcoordination_5fconfig_2eproto
