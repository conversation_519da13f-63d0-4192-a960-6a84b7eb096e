# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/lite/profiling/proto/model_runtime_info.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.lite.profiling.proto import profiling_info_pb2 as tensorflow_dot_lite_dot_profiling_dot_proto_dot_profiling__info__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8tensorflow/lite/profiling/proto/model_runtime_info.proto\x12\x10tflite.profiling\x1a\x34tensorflow/lite/profiling/proto/profiling_info.proto\"_\n\x13ModelRuntimeDetails\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x34\n\tsubgraphs\x18\x02 \x03(\x0b\x32!.tflite.profiling.RuntimeSubgraph\"\xb7\x02\n\x0fRuntimeSubgraph\x12\x13\n\x0bsubgraph_id\x18\x01 \x01(\x05\x12%\n\x05\x65\x64ges\x18\x02 \x03(\x0b\x32\x16.tflite.profiling.Edge\x12%\n\x05nodes\x18\x03 \x03(\x0b\x32\x16.tflite.profiling.Node\x12\x1a\n\x0e\x65xecution_plan\x18\x04 \x03(\x05\x42\x02\x10\x01\x12\x45\n\rsubgraph_type\x18\x05 \x01(\x0e\x32..tflite.profiling.RuntimeSubgraph.SubgraphType\x12\x0c\n\x04name\x18\x06 \x01(\t\"P\n\x0cSubgraphType\x12\x14\n\x10UNKNOWN_SUBGRAPH\x10\x00\x12\x13\n\x0fTFLITE_SUBGRAPH\x10\x01\x12\x15\n\x11\x44\x45LEGATE_SUBGRAPH\x10\x02\"\xba\x02\n\x04Node\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x12\n\x06inputs\x18\x04 \x03(\x05\x42\x02\x10\x01\x12\x13\n\x07outputs\x18\x05 \x03(\x05\x42\x02\x10\x01\x12\x19\n\rintermediates\x18\x06 \x03(\x05\x42\x02\x10\x01\x12\x17\n\x0btemporaries\x18\x07 \x03(\x05\x42\x02\x10\x01\x12\x38\n\x0fop_profile_data\x18\n \x01(\x0b\x32\x1f.tflite.profiling.OpProfileData\x12\x46\n\x15\x64\x65legate_node_details\x18\x08 \x01(\x0b\x32%.tflite.profiling.DelegateNodeDetailsH\x00\x12\x1e\n\x14\x64\x65legated_to_node_id\x18\t \x01(\x05H\x00\x42\x0b\n\tnode_info\"R\n\x13\x44\x65legateNodeDetails\x12\x15\n\rdelegate_name\x18\x01 \x01(\t\x12$\n\x18tflite_node_ids_replaced\x18\x02 \x03(\x05\x42\x02\x10\x01\"\x81\x05\n\x04\x45\x64ge\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x32\n\tdata_type\x18\x03 \x01(\x0e\x32\x1f.tflite.profiling.Edge.DataType\x12\x11\n\x05shape\x18\x04 \x03(\x05\x42\x02\x10\x01\x12\x17\n\x0f\x61llocation_type\x18\x05 \x01(\t\x12\x36\n\x0blayout_type\x18\x06 \x01(\x0e\x32!.tflite.profiling.Edge.LayoutType\x12\x0c\n\x04size\x18\x07 \x01(\x05\"\x85\x02\n\x08\x44\x61taType\x12\x10\n\x0cUNKNOWN_TYPE\x10\x00\x12\x0b\n\x07\x46LOAT32\x10\x01\x12\t\n\x05INT32\x10\x02\x12\t\n\x05UINT8\x10\x03\x12\t\n\x05INT64\x10\x04\x12\n\n\x06STRING\x10\x05\x12\x08\n\x04\x42OOL\x10\x06\x12\t\n\x05INT16\x10\x07\x12\r\n\tCOMPLEX64\x10\x08\x12\x08\n\x04INT8\x10\t\x12\x0b\n\x07\x46LOAT16\x10\n\x12\x0b\n\x07\x46LOAT64\x10\x0b\x12\x0e\n\nCOMPLEX128\x10\x0c\x12\n\n\x06UINT64\x10\r\x12\x0c\n\x08RESOURCE\x10\x0e\x12\x0b\n\x07VARIANT\x10\x0f\x12\n\n\x06UINT32\x10\x10\x12\n\n\x06UINT16\x10\x11\x12\x08\n\x04INT4\x10\x12\x12\x0c\n\x08\x42\x46LOAT16\x10\x13\"\xb0\x01\n\nLayoutType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\n\n\x06SCALAR\x10\x01\x12\n\n\x06LINEAR\x10\x02\x12\x06\n\x02HW\x10\x03\x12\x07\n\x03\x43HW\x10\x04\x12\x07\n\x03HWC\x10\x05\x12\x08\n\x04OIHW\x10\x06\x12\x08\n\x04OHWI\x10\x07\x12\x08\n\x04IHWO\x10\x08\x12\x08\n\x04IOHW\x10\t\x12\x08\n\x04\x42HWC\x10\n\x12\x08\n\x04HWDC\x10\x0b\x12\t\n\x05\x42HWDC\x10\x0c\x12\x07\n\x03HWD\x10\r\x12\t\n\x05OHWDI\x10\x0e\x12\x08\n\x04HWIO\x10\x0f\x42\x02P\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.lite.profiling.proto.model_runtime_info_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'P\001'
  _RUNTIMESUBGRAPH.fields_by_name['execution_plan']._options = None
  _RUNTIMESUBGRAPH.fields_by_name['execution_plan']._serialized_options = b'\020\001'
  _NODE.fields_by_name['inputs']._options = None
  _NODE.fields_by_name['inputs']._serialized_options = b'\020\001'
  _NODE.fields_by_name['outputs']._options = None
  _NODE.fields_by_name['outputs']._serialized_options = b'\020\001'
  _NODE.fields_by_name['intermediates']._options = None
  _NODE.fields_by_name['intermediates']._serialized_options = b'\020\001'
  _NODE.fields_by_name['temporaries']._options = None
  _NODE.fields_by_name['temporaries']._serialized_options = b'\020\001'
  _DELEGATENODEDETAILS.fields_by_name['tflite_node_ids_replaced']._options = None
  _DELEGATENODEDETAILS.fields_by_name['tflite_node_ids_replaced']._serialized_options = b'\020\001'
  _EDGE.fields_by_name['shape']._options = None
  _EDGE.fields_by_name['shape']._serialized_options = b'\020\001'
  _MODELRUNTIMEDETAILS._serialized_start=132
  _MODELRUNTIMEDETAILS._serialized_end=227
  _RUNTIMESUBGRAPH._serialized_start=230
  _RUNTIMESUBGRAPH._serialized_end=541
  _RUNTIMESUBGRAPH_SUBGRAPHTYPE._serialized_start=461
  _RUNTIMESUBGRAPH_SUBGRAPHTYPE._serialized_end=541
  _NODE._serialized_start=544
  _NODE._serialized_end=858
  _DELEGATENODEDETAILS._serialized_start=860
  _DELEGATENODEDETAILS._serialized_end=942
  _EDGE._serialized_start=945
  _EDGE._serialized_end=1586
  _EDGE_DATATYPE._serialized_start=1146
  _EDGE_DATATYPE._serialized_end=1407
  _EDGE_LAYOUTTYPE._serialized_start=1410
  _EDGE_LAYOUTTYPE._serialized_end=1586
# @@protoc_insertion_point(module_scope)
