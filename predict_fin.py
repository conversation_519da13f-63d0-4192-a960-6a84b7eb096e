# -*- coding: utf-8 -*-
# MLP for Pima Indians Dataset Serialize to JSON and HDF5
import numpy as np
from PIL import Image



# Handle Keras 2.x to 3.x compatibility issue
import warnings
warnings.filterwarnings('ignore')

# Manually reconstruct the model architecture for Keras 3.x compatibility

from keras.models import Sequential
from keras.layers import Conv2D, MaxPooling2D, BatchNormalization, Dropout, Flatten, <PERSON><PERSON>

def create_model_architecture():
    """Recreate the model architecture based on the JSON configuration"""
    model = Sequential()

    # First Conv2D layer
    model.add(Conv2D(32, (3, 3), activation='relu', input_shape=(128, 128, 3), name='conv2d_26'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_26'))
    model.add(BatchNormalization(name='batch_normalization_26'))

    # Second Conv2D layer
    model.add(Conv2D(64, (3, 3), activation='relu', name='conv2d_27'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_27'))
    model.add(BatchNormalization(name='batch_normalization_27'))

    # Third Conv2D layer
    model.add(Conv2D(64, (3, 3), activation='relu', name='conv2d_28'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_28'))
    model.add(BatchNormalization(name='batch_normalization_28'))

    # Fourth Conv2D layer
    model.add(Conv2D(96, (3, 3), activation='relu', name='conv2d_29'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_29'))
    model.add(BatchNormalization(name='batch_normalization_29'))

    # Fifth Conv2D layer
    model.add(Conv2D(32, (3, 3), activation='relu', name='conv2d_30'))
    model.add(MaxPooling2D((2, 2), name='max_pooling2d_30'))
    model.add(BatchNormalization(name='batch_normalization_30'))

    # Dropout and Dense layers
    model.add(Dropout(0.2, name='dropout_11'))
    model.add(Flatten(name='flatten_6'))
    model.add(Dense(128, activation='relu', name='dense_11'))
    model.add(Dropout(0.3, name='dropout_12'))
    model.add(Dense(25, activation='softmax', name='dense_12'))  # 25 classes based on label array

    return model

# Create the model and load weights
try:
    loaded_model = create_model_architecture()
    loaded_model.load_weights("model1.h5")
    print("Successfully recreated model architecture and loaded weights")
except Exception as e:
    print(f"Failed to load model: {e}")
    raise e


label=["Apple___Apple_scab","Apple___Black_rot","Apple___Cedar_apple_rust","Apple___Healthy",
       "Grape___Black_rot","Grape___Esca_(Black_Measles)","Grape___Healthy",
       "Grape___Leaf_blight_(Isariopsis_Leaf_Spot)",
       "Potato___Early_blight","Potato___Healthy","Tomato___Bacterial_spot",
       "Tomato___Healthy","Tomato___Leaf_Mold",
       "Tomato___Tomato_Yellow_Leaf_Curl_Virus","Tomato___Tomato_mosaic_virus"]

test_image = Image.open('Training_purpose/TYLCV1.png').resize((128, 128))
# Convert RGBA to RGB if necessary
if test_image.mode == 'RGBA':
    test_image = test_image.convert('RGB')
test_image = np.array(test_image)
test_image = np.expand_dims(test_image, axis = 0)
result = loaded_model.predict(test_image)
print(result)
fresult=np.max(result)
label2=label[result.argmax()]
print(label2)






















'''
a=np.round(result[0][0])
b=np.round(result[0][1])
c=np.round(result[0][2])
d=np.round(result[0][3])

print(a)
print(b)
print(c)
print(d)
'''

'''
label=["cats","dogs","horse","rose"]
test_image=cv2.imread("18.jpg")

tf=test_image.reshape(-1,test_image.shape[0],test_image.shape[1],test_image.shape[2])

prediction=loaded_model.predict_classes(tf)
fresult=np.max(prediction)
label2=label[prediction.argmax()]
print(label2)'''

'''
if result[0][0] == 1:
    prediction = 'dog'
    print(prediction)
elif result[0][1] == 1:
    prediction = 'cat'
    print(prediction)
elif result[0][2]== 1:
    prediction = 'Horse'
    print(prediction)
elif result[0][3] == 1:
    prediction = 'rose'
    print(prediction)
'''


