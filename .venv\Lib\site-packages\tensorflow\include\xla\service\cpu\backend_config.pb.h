// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/cpu/backend_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "xla/service/cpu/onednn_config.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto;
namespace xla {
namespace cpu {
class BackendConfig;
struct BackendConfigDefaultTypeInternal;
extern BackendConfigDefaultTypeInternal _BackendConfig_default_instance_;
class CustomCallBackendConfig;
struct CustomCallBackendConfigDefaultTypeInternal;
extern CustomCallBackendConfigDefaultTypeInternal _CustomCallBackendConfig_default_instance_;
class FusionBackendConfig;
struct FusionBackendConfigDefaultTypeInternal;
extern FusionBackendConfigDefaultTypeInternal _FusionBackendConfig_default_instance_;
}  // namespace cpu
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::cpu::BackendConfig* Arena::CreateMaybeMessage<::xla::cpu::BackendConfig>(Arena*);
template<> ::xla::cpu::CustomCallBackendConfig* Arena::CreateMaybeMessage<::xla::cpu::CustomCallBackendConfig>(Arena*);
template<> ::xla::cpu::FusionBackendConfig* Arena::CreateMaybeMessage<::xla::cpu::FusionBackendConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace cpu {

// ===================================================================

class CustomCallBackendConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.CustomCallBackendConfig) */ {
 public:
  inline CustomCallBackendConfig() : CustomCallBackendConfig(nullptr) {}
  ~CustomCallBackendConfig() override;
  explicit PROTOBUF_CONSTEXPR CustomCallBackendConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CustomCallBackendConfig(const CustomCallBackendConfig& from);
  CustomCallBackendConfig(CustomCallBackendConfig&& from) noexcept
    : CustomCallBackendConfig() {
    *this = ::std::move(from);
  }

  inline CustomCallBackendConfig& operator=(const CustomCallBackendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline CustomCallBackendConfig& operator=(CustomCallBackendConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CustomCallBackendConfig& default_instance() {
    return *internal_default_instance();
  }
  enum RawBackendConfigOneofCase {
    kOpaque = 1,
    kAttributes = 2,
    RAW_BACKEND_CONFIG_ONEOF_NOT_SET = 0,
  };

  static inline const CustomCallBackendConfig* internal_default_instance() {
    return reinterpret_cast<const CustomCallBackendConfig*>(
               &_CustomCallBackendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CustomCallBackendConfig& a, CustomCallBackendConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(CustomCallBackendConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CustomCallBackendConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CustomCallBackendConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CustomCallBackendConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CustomCallBackendConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CustomCallBackendConfig& from) {
    CustomCallBackendConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CustomCallBackendConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.CustomCallBackendConfig";
  }
  protected:
  explicit CustomCallBackendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpaqueFieldNumber = 1,
    kAttributesFieldNumber = 2,
  };
  // string opaque = 1;
  bool has_opaque() const;
  private:
  bool _internal_has_opaque() const;
  public:
  void clear_opaque();
  const std::string& opaque() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_opaque(ArgT0&& arg0, ArgT... args);
  std::string* mutable_opaque();
  PROTOBUF_NODISCARD std::string* release_opaque();
  void set_allocated_opaque(std::string* opaque);
  private:
  const std::string& _internal_opaque() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_opaque(const std::string& value);
  std::string* _internal_mutable_opaque();
  public:

  // string attributes = 2;
  bool has_attributes() const;
  private:
  bool _internal_has_attributes() const;
  public:
  void clear_attributes();
  const std::string& attributes() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_attributes(ArgT0&& arg0, ArgT... args);
  std::string* mutable_attributes();
  PROTOBUF_NODISCARD std::string* release_attributes();
  void set_allocated_attributes(std::string* attributes);
  private:
  const std::string& _internal_attributes() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_attributes(const std::string& value);
  std::string* _internal_mutable_attributes();
  public:

  void clear_raw_backend_config_oneof();
  RawBackendConfigOneofCase raw_backend_config_oneof_case() const;
  // @@protoc_insertion_point(class_scope:xla.cpu.CustomCallBackendConfig)
 private:
  class _Internal;
  void set_has_opaque();
  void set_has_attributes();

  inline bool has_raw_backend_config_oneof() const;
  inline void clear_has_raw_backend_config_oneof();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union RawBackendConfigOneofUnion {
      constexpr RawBackendConfigOneofUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr opaque_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr attributes_;
    } raw_backend_config_oneof_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class FusionBackendConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.FusionBackendConfig) */ {
 public:
  inline FusionBackendConfig() : FusionBackendConfig(nullptr) {}
  ~FusionBackendConfig() override;
  explicit PROTOBUF_CONSTEXPR FusionBackendConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FusionBackendConfig(const FusionBackendConfig& from);
  FusionBackendConfig(FusionBackendConfig&& from) noexcept
    : FusionBackendConfig() {
    *this = ::std::move(from);
  }

  inline FusionBackendConfig& operator=(const FusionBackendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline FusionBackendConfig& operator=(FusionBackendConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FusionBackendConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const FusionBackendConfig* internal_default_instance() {
    return reinterpret_cast<const FusionBackendConfig*>(
               &_FusionBackendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FusionBackendConfig& a, FusionBackendConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(FusionBackendConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FusionBackendConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FusionBackendConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FusionBackendConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FusionBackendConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FusionBackendConfig& from) {
    FusionBackendConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FusionBackendConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.FusionBackendConfig";
  }
  protected:
  explicit FusionBackendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKindFieldNumber = 1,
  };
  // string kind = 1;
  void clear_kind();
  const std::string& kind() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_kind(ArgT0&& arg0, ArgT... args);
  std::string* mutable_kind();
  PROTOBUF_NODISCARD std::string* release_kind();
  void set_allocated_kind(std::string* kind);
  private:
  const std::string& _internal_kind() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_kind(const std::string& value);
  std::string* _internal_mutable_kind();
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.FusionBackendConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class BackendConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.BackendConfig) */ {
 public:
  inline BackendConfig() : BackendConfig(nullptr) {}
  ~BackendConfig() override;
  explicit PROTOBUF_CONSTEXPR BackendConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BackendConfig(const BackendConfig& from);
  BackendConfig(BackendConfig&& from) noexcept
    : BackendConfig() {
    *this = ::std::move(from);
  }

  inline BackendConfig& operator=(const BackendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline BackendConfig& operator=(BackendConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BackendConfig& default_instance() {
    return *internal_default_instance();
  }
  enum BackendConfigOneofCase {
    kOnednnMatmulConfig = 2,
    kOnednnLayerNormConfig = 3,
    kOnednnSoftmaxConfig = 4,
    kOnednnConvConfig = 5,
    kCustomCallConfig = 6,
    kFusionConfig = 7,
    BACKEND_CONFIG_ONEOF_NOT_SET = 0,
  };

  static inline const BackendConfig* internal_default_instance() {
    return reinterpret_cast<const BackendConfig*>(
               &_BackendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(BackendConfig& a, BackendConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(BackendConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BackendConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BackendConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BackendConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BackendConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BackendConfig& from) {
    BackendConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BackendConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.BackendConfig";
  }
  protected:
  explicit BackendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOuterDimensionPartitionsFieldNumber = 1,
    kOnednnMatmulConfigFieldNumber = 2,
    kOnednnLayerNormConfigFieldNumber = 3,
    kOnednnSoftmaxConfigFieldNumber = 4,
    kOnednnConvConfigFieldNumber = 5,
    kCustomCallConfigFieldNumber = 6,
    kFusionConfigFieldNumber = 7,
  };
  // repeated int64 outer_dimension_partitions = 1;
  int outer_dimension_partitions_size() const;
  private:
  int _internal_outer_dimension_partitions_size() const;
  public:
  void clear_outer_dimension_partitions();
  private:
  int64_t _internal_outer_dimension_partitions(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_outer_dimension_partitions() const;
  void _internal_add_outer_dimension_partitions(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_outer_dimension_partitions();
  public:
  int64_t outer_dimension_partitions(int index) const;
  void set_outer_dimension_partitions(int index, int64_t value);
  void add_outer_dimension_partitions(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      outer_dimension_partitions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_outer_dimension_partitions();

  // .xla.cpu.OneDnnMatMulConfig onednn_matmul_config = 2;
  bool has_onednn_matmul_config() const;
  private:
  bool _internal_has_onednn_matmul_config() const;
  public:
  void clear_onednn_matmul_config();
  const ::xla::cpu::OneDnnMatMulConfig& onednn_matmul_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnMatMulConfig* release_onednn_matmul_config();
  ::xla::cpu::OneDnnMatMulConfig* mutable_onednn_matmul_config();
  void set_allocated_onednn_matmul_config(::xla::cpu::OneDnnMatMulConfig* onednn_matmul_config);
  private:
  const ::xla::cpu::OneDnnMatMulConfig& _internal_onednn_matmul_config() const;
  ::xla::cpu::OneDnnMatMulConfig* _internal_mutable_onednn_matmul_config();
  public:
  void unsafe_arena_set_allocated_onednn_matmul_config(
      ::xla::cpu::OneDnnMatMulConfig* onednn_matmul_config);
  ::xla::cpu::OneDnnMatMulConfig* unsafe_arena_release_onednn_matmul_config();

  // .xla.cpu.OneDnnNormConfig onednn_layer_norm_config = 3;
  bool has_onednn_layer_norm_config() const;
  private:
  bool _internal_has_onednn_layer_norm_config() const;
  public:
  void clear_onednn_layer_norm_config();
  const ::xla::cpu::OneDnnNormConfig& onednn_layer_norm_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnNormConfig* release_onednn_layer_norm_config();
  ::xla::cpu::OneDnnNormConfig* mutable_onednn_layer_norm_config();
  void set_allocated_onednn_layer_norm_config(::xla::cpu::OneDnnNormConfig* onednn_layer_norm_config);
  private:
  const ::xla::cpu::OneDnnNormConfig& _internal_onednn_layer_norm_config() const;
  ::xla::cpu::OneDnnNormConfig* _internal_mutable_onednn_layer_norm_config();
  public:
  void unsafe_arena_set_allocated_onednn_layer_norm_config(
      ::xla::cpu::OneDnnNormConfig* onednn_layer_norm_config);
  ::xla::cpu::OneDnnNormConfig* unsafe_arena_release_onednn_layer_norm_config();

  // .xla.cpu.OneDnnSoftmaxConfig onednn_softmax_config = 4;
  bool has_onednn_softmax_config() const;
  private:
  bool _internal_has_onednn_softmax_config() const;
  public:
  void clear_onednn_softmax_config();
  const ::xla::cpu::OneDnnSoftmaxConfig& onednn_softmax_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnSoftmaxConfig* release_onednn_softmax_config();
  ::xla::cpu::OneDnnSoftmaxConfig* mutable_onednn_softmax_config();
  void set_allocated_onednn_softmax_config(::xla::cpu::OneDnnSoftmaxConfig* onednn_softmax_config);
  private:
  const ::xla::cpu::OneDnnSoftmaxConfig& _internal_onednn_softmax_config() const;
  ::xla::cpu::OneDnnSoftmaxConfig* _internal_mutable_onednn_softmax_config();
  public:
  void unsafe_arena_set_allocated_onednn_softmax_config(
      ::xla::cpu::OneDnnSoftmaxConfig* onednn_softmax_config);
  ::xla::cpu::OneDnnSoftmaxConfig* unsafe_arena_release_onednn_softmax_config();

  // .xla.cpu.OneDnnConvolutionConfig onednn_conv_config = 5;
  bool has_onednn_conv_config() const;
  private:
  bool _internal_has_onednn_conv_config() const;
  public:
  void clear_onednn_conv_config();
  const ::xla::cpu::OneDnnConvolutionConfig& onednn_conv_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnConvolutionConfig* release_onednn_conv_config();
  ::xla::cpu::OneDnnConvolutionConfig* mutable_onednn_conv_config();
  void set_allocated_onednn_conv_config(::xla::cpu::OneDnnConvolutionConfig* onednn_conv_config);
  private:
  const ::xla::cpu::OneDnnConvolutionConfig& _internal_onednn_conv_config() const;
  ::xla::cpu::OneDnnConvolutionConfig* _internal_mutable_onednn_conv_config();
  public:
  void unsafe_arena_set_allocated_onednn_conv_config(
      ::xla::cpu::OneDnnConvolutionConfig* onednn_conv_config);
  ::xla::cpu::OneDnnConvolutionConfig* unsafe_arena_release_onednn_conv_config();

  // .xla.cpu.CustomCallBackendConfig custom_call_config = 6;
  bool has_custom_call_config() const;
  private:
  bool _internal_has_custom_call_config() const;
  public:
  void clear_custom_call_config();
  const ::xla::cpu::CustomCallBackendConfig& custom_call_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::CustomCallBackendConfig* release_custom_call_config();
  ::xla::cpu::CustomCallBackendConfig* mutable_custom_call_config();
  void set_allocated_custom_call_config(::xla::cpu::CustomCallBackendConfig* custom_call_config);
  private:
  const ::xla::cpu::CustomCallBackendConfig& _internal_custom_call_config() const;
  ::xla::cpu::CustomCallBackendConfig* _internal_mutable_custom_call_config();
  public:
  void unsafe_arena_set_allocated_custom_call_config(
      ::xla::cpu::CustomCallBackendConfig* custom_call_config);
  ::xla::cpu::CustomCallBackendConfig* unsafe_arena_release_custom_call_config();

  // .xla.cpu.FusionBackendConfig fusion_config = 7;
  bool has_fusion_config() const;
  private:
  bool _internal_has_fusion_config() const;
  public:
  void clear_fusion_config();
  const ::xla::cpu::FusionBackendConfig& fusion_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::FusionBackendConfig* release_fusion_config();
  ::xla::cpu::FusionBackendConfig* mutable_fusion_config();
  void set_allocated_fusion_config(::xla::cpu::FusionBackendConfig* fusion_config);
  private:
  const ::xla::cpu::FusionBackendConfig& _internal_fusion_config() const;
  ::xla::cpu::FusionBackendConfig* _internal_mutable_fusion_config();
  public:
  void unsafe_arena_set_allocated_fusion_config(
      ::xla::cpu::FusionBackendConfig* fusion_config);
  ::xla::cpu::FusionBackendConfig* unsafe_arena_release_fusion_config();

  void clear_backend_config_oneof();
  BackendConfigOneofCase backend_config_oneof_case() const;
  // @@protoc_insertion_point(class_scope:xla.cpu.BackendConfig)
 private:
  class _Internal;
  void set_has_onednn_matmul_config();
  void set_has_onednn_layer_norm_config();
  void set_has_onednn_softmax_config();
  void set_has_onednn_conv_config();
  void set_has_custom_call_config();
  void set_has_fusion_config();

  inline bool has_backend_config_oneof() const;
  inline void clear_has_backend_config_oneof();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > outer_dimension_partitions_;
    mutable std::atomic<int> _outer_dimension_partitions_cached_byte_size_;
    union BackendConfigOneofUnion {
      constexpr BackendConfigOneofUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::xla::cpu::OneDnnMatMulConfig* onednn_matmul_config_;
      ::xla::cpu::OneDnnNormConfig* onednn_layer_norm_config_;
      ::xla::cpu::OneDnnSoftmaxConfig* onednn_softmax_config_;
      ::xla::cpu::OneDnnConvolutionConfig* onednn_conv_config_;
      ::xla::cpu::CustomCallBackendConfig* custom_call_config_;
      ::xla::cpu::FusionBackendConfig* fusion_config_;
    } backend_config_oneof_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CustomCallBackendConfig

// string opaque = 1;
inline bool CustomCallBackendConfig::_internal_has_opaque() const {
  return raw_backend_config_oneof_case() == kOpaque;
}
inline bool CustomCallBackendConfig::has_opaque() const {
  return _internal_has_opaque();
}
inline void CustomCallBackendConfig::set_has_opaque() {
  _impl_._oneof_case_[0] = kOpaque;
}
inline void CustomCallBackendConfig::clear_opaque() {
  if (_internal_has_opaque()) {
    _impl_.raw_backend_config_oneof_.opaque_.Destroy();
    clear_has_raw_backend_config_oneof();
  }
}
inline const std::string& CustomCallBackendConfig::opaque() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CustomCallBackendConfig.opaque)
  return _internal_opaque();
}
template <typename ArgT0, typename... ArgT>
inline void CustomCallBackendConfig::set_opaque(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_opaque()) {
    clear_raw_backend_config_oneof();
    set_has_opaque();
    _impl_.raw_backend_config_oneof_.opaque_.InitDefault();
  }
  _impl_.raw_backend_config_oneof_.opaque_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.cpu.CustomCallBackendConfig.opaque)
}
inline std::string* CustomCallBackendConfig::mutable_opaque() {
  std::string* _s = _internal_mutable_opaque();
  // @@protoc_insertion_point(field_mutable:xla.cpu.CustomCallBackendConfig.opaque)
  return _s;
}
inline const std::string& CustomCallBackendConfig::_internal_opaque() const {
  if (_internal_has_opaque()) {
    return _impl_.raw_backend_config_oneof_.opaque_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void CustomCallBackendConfig::_internal_set_opaque(const std::string& value) {
  if (!_internal_has_opaque()) {
    clear_raw_backend_config_oneof();
    set_has_opaque();
    _impl_.raw_backend_config_oneof_.opaque_.InitDefault();
  }
  _impl_.raw_backend_config_oneof_.opaque_.Set(value, GetArenaForAllocation());
}
inline std::string* CustomCallBackendConfig::_internal_mutable_opaque() {
  if (!_internal_has_opaque()) {
    clear_raw_backend_config_oneof();
    set_has_opaque();
    _impl_.raw_backend_config_oneof_.opaque_.InitDefault();
  }
  return _impl_.raw_backend_config_oneof_.opaque_.Mutable(      GetArenaForAllocation());
}
inline std::string* CustomCallBackendConfig::release_opaque() {
  // @@protoc_insertion_point(field_release:xla.cpu.CustomCallBackendConfig.opaque)
  if (_internal_has_opaque()) {
    clear_has_raw_backend_config_oneof();
    return _impl_.raw_backend_config_oneof_.opaque_.Release();
  } else {
    return nullptr;
  }
}
inline void CustomCallBackendConfig::set_allocated_opaque(std::string* opaque) {
  if (has_raw_backend_config_oneof()) {
    clear_raw_backend_config_oneof();
  }
  if (opaque != nullptr) {
    set_has_opaque();
    _impl_.raw_backend_config_oneof_.opaque_.InitAllocated(opaque, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.CustomCallBackendConfig.opaque)
}

// string attributes = 2;
inline bool CustomCallBackendConfig::_internal_has_attributes() const {
  return raw_backend_config_oneof_case() == kAttributes;
}
inline bool CustomCallBackendConfig::has_attributes() const {
  return _internal_has_attributes();
}
inline void CustomCallBackendConfig::set_has_attributes() {
  _impl_._oneof_case_[0] = kAttributes;
}
inline void CustomCallBackendConfig::clear_attributes() {
  if (_internal_has_attributes()) {
    _impl_.raw_backend_config_oneof_.attributes_.Destroy();
    clear_has_raw_backend_config_oneof();
  }
}
inline const std::string& CustomCallBackendConfig::attributes() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CustomCallBackendConfig.attributes)
  return _internal_attributes();
}
template <typename ArgT0, typename... ArgT>
inline void CustomCallBackendConfig::set_attributes(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_attributes()) {
    clear_raw_backend_config_oneof();
    set_has_attributes();
    _impl_.raw_backend_config_oneof_.attributes_.InitDefault();
  }
  _impl_.raw_backend_config_oneof_.attributes_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.cpu.CustomCallBackendConfig.attributes)
}
inline std::string* CustomCallBackendConfig::mutable_attributes() {
  std::string* _s = _internal_mutable_attributes();
  // @@protoc_insertion_point(field_mutable:xla.cpu.CustomCallBackendConfig.attributes)
  return _s;
}
inline const std::string& CustomCallBackendConfig::_internal_attributes() const {
  if (_internal_has_attributes()) {
    return _impl_.raw_backend_config_oneof_.attributes_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void CustomCallBackendConfig::_internal_set_attributes(const std::string& value) {
  if (!_internal_has_attributes()) {
    clear_raw_backend_config_oneof();
    set_has_attributes();
    _impl_.raw_backend_config_oneof_.attributes_.InitDefault();
  }
  _impl_.raw_backend_config_oneof_.attributes_.Set(value, GetArenaForAllocation());
}
inline std::string* CustomCallBackendConfig::_internal_mutable_attributes() {
  if (!_internal_has_attributes()) {
    clear_raw_backend_config_oneof();
    set_has_attributes();
    _impl_.raw_backend_config_oneof_.attributes_.InitDefault();
  }
  return _impl_.raw_backend_config_oneof_.attributes_.Mutable(      GetArenaForAllocation());
}
inline std::string* CustomCallBackendConfig::release_attributes() {
  // @@protoc_insertion_point(field_release:xla.cpu.CustomCallBackendConfig.attributes)
  if (_internal_has_attributes()) {
    clear_has_raw_backend_config_oneof();
    return _impl_.raw_backend_config_oneof_.attributes_.Release();
  } else {
    return nullptr;
  }
}
inline void CustomCallBackendConfig::set_allocated_attributes(std::string* attributes) {
  if (has_raw_backend_config_oneof()) {
    clear_raw_backend_config_oneof();
  }
  if (attributes != nullptr) {
    set_has_attributes();
    _impl_.raw_backend_config_oneof_.attributes_.InitAllocated(attributes, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.CustomCallBackendConfig.attributes)
}

inline bool CustomCallBackendConfig::has_raw_backend_config_oneof() const {
  return raw_backend_config_oneof_case() != RAW_BACKEND_CONFIG_ONEOF_NOT_SET;
}
inline void CustomCallBackendConfig::clear_has_raw_backend_config_oneof() {
  _impl_._oneof_case_[0] = RAW_BACKEND_CONFIG_ONEOF_NOT_SET;
}
inline CustomCallBackendConfig::RawBackendConfigOneofCase CustomCallBackendConfig::raw_backend_config_oneof_case() const {
  return CustomCallBackendConfig::RawBackendConfigOneofCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// FusionBackendConfig

// string kind = 1;
inline void FusionBackendConfig::clear_kind() {
  _impl_.kind_.ClearToEmpty();
}
inline const std::string& FusionBackendConfig::kind() const {
  // @@protoc_insertion_point(field_get:xla.cpu.FusionBackendConfig.kind)
  return _internal_kind();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FusionBackendConfig::set_kind(ArgT0&& arg0, ArgT... args) {
 
 _impl_.kind_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.cpu.FusionBackendConfig.kind)
}
inline std::string* FusionBackendConfig::mutable_kind() {
  std::string* _s = _internal_mutable_kind();
  // @@protoc_insertion_point(field_mutable:xla.cpu.FusionBackendConfig.kind)
  return _s;
}
inline const std::string& FusionBackendConfig::_internal_kind() const {
  return _impl_.kind_.Get();
}
inline void FusionBackendConfig::_internal_set_kind(const std::string& value) {
  
  _impl_.kind_.Set(value, GetArenaForAllocation());
}
inline std::string* FusionBackendConfig::_internal_mutable_kind() {
  
  return _impl_.kind_.Mutable(GetArenaForAllocation());
}
inline std::string* FusionBackendConfig::release_kind() {
  // @@protoc_insertion_point(field_release:xla.cpu.FusionBackendConfig.kind)
  return _impl_.kind_.Release();
}
inline void FusionBackendConfig::set_allocated_kind(std::string* kind) {
  if (kind != nullptr) {
    
  } else {
    
  }
  _impl_.kind_.SetAllocated(kind, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.kind_.IsDefault()) {
    _impl_.kind_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.FusionBackendConfig.kind)
}

// -------------------------------------------------------------------

// BackendConfig

// repeated int64 outer_dimension_partitions = 1;
inline int BackendConfig::_internal_outer_dimension_partitions_size() const {
  return _impl_.outer_dimension_partitions_.size();
}
inline int BackendConfig::outer_dimension_partitions_size() const {
  return _internal_outer_dimension_partitions_size();
}
inline void BackendConfig::clear_outer_dimension_partitions() {
  _impl_.outer_dimension_partitions_.Clear();
}
inline int64_t BackendConfig::_internal_outer_dimension_partitions(int index) const {
  return _impl_.outer_dimension_partitions_.Get(index);
}
inline int64_t BackendConfig::outer_dimension_partitions(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.outer_dimension_partitions)
  return _internal_outer_dimension_partitions(index);
}
inline void BackendConfig::set_outer_dimension_partitions(int index, int64_t value) {
  _impl_.outer_dimension_partitions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.BackendConfig.outer_dimension_partitions)
}
inline void BackendConfig::_internal_add_outer_dimension_partitions(int64_t value) {
  _impl_.outer_dimension_partitions_.Add(value);
}
inline void BackendConfig::add_outer_dimension_partitions(int64_t value) {
  _internal_add_outer_dimension_partitions(value);
  // @@protoc_insertion_point(field_add:xla.cpu.BackendConfig.outer_dimension_partitions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
BackendConfig::_internal_outer_dimension_partitions() const {
  return _impl_.outer_dimension_partitions_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
BackendConfig::outer_dimension_partitions() const {
  // @@protoc_insertion_point(field_list:xla.cpu.BackendConfig.outer_dimension_partitions)
  return _internal_outer_dimension_partitions();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
BackendConfig::_internal_mutable_outer_dimension_partitions() {
  return &_impl_.outer_dimension_partitions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
BackendConfig::mutable_outer_dimension_partitions() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.BackendConfig.outer_dimension_partitions)
  return _internal_mutable_outer_dimension_partitions();
}

// .xla.cpu.OneDnnMatMulConfig onednn_matmul_config = 2;
inline bool BackendConfig::_internal_has_onednn_matmul_config() const {
  return backend_config_oneof_case() == kOnednnMatmulConfig;
}
inline bool BackendConfig::has_onednn_matmul_config() const {
  return _internal_has_onednn_matmul_config();
}
inline void BackendConfig::set_has_onednn_matmul_config() {
  _impl_._oneof_case_[0] = kOnednnMatmulConfig;
}
inline ::xla::cpu::OneDnnMatMulConfig* BackendConfig::release_onednn_matmul_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.BackendConfig.onednn_matmul_config)
  if (_internal_has_onednn_matmul_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnMatMulConfig* temp = _impl_.backend_config_oneof_.onednn_matmul_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.backend_config_oneof_.onednn_matmul_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnMatMulConfig& BackendConfig::_internal_onednn_matmul_config() const {
  return _internal_has_onednn_matmul_config()
      ? *_impl_.backend_config_oneof_.onednn_matmul_config_
      : reinterpret_cast< ::xla::cpu::OneDnnMatMulConfig&>(::xla::cpu::_OneDnnMatMulConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnMatMulConfig& BackendConfig::onednn_matmul_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.onednn_matmul_config)
  return _internal_onednn_matmul_config();
}
inline ::xla::cpu::OneDnnMatMulConfig* BackendConfig::unsafe_arena_release_onednn_matmul_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.BackendConfig.onednn_matmul_config)
  if (_internal_has_onednn_matmul_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnMatMulConfig* temp = _impl_.backend_config_oneof_.onednn_matmul_config_;
    _impl_.backend_config_oneof_.onednn_matmul_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BackendConfig::unsafe_arena_set_allocated_onednn_matmul_config(::xla::cpu::OneDnnMatMulConfig* onednn_matmul_config) {
  clear_backend_config_oneof();
  if (onednn_matmul_config) {
    set_has_onednn_matmul_config();
    _impl_.backend_config_oneof_.onednn_matmul_config_ = onednn_matmul_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.BackendConfig.onednn_matmul_config)
}
inline ::xla::cpu::OneDnnMatMulConfig* BackendConfig::_internal_mutable_onednn_matmul_config() {
  if (!_internal_has_onednn_matmul_config()) {
    clear_backend_config_oneof();
    set_has_onednn_matmul_config();
    _impl_.backend_config_oneof_.onednn_matmul_config_ = CreateMaybeMessage< ::xla::cpu::OneDnnMatMulConfig >(GetArenaForAllocation());
  }
  return _impl_.backend_config_oneof_.onednn_matmul_config_;
}
inline ::xla::cpu::OneDnnMatMulConfig* BackendConfig::mutable_onednn_matmul_config() {
  ::xla::cpu::OneDnnMatMulConfig* _msg = _internal_mutable_onednn_matmul_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.BackendConfig.onednn_matmul_config)
  return _msg;
}

// .xla.cpu.OneDnnNormConfig onednn_layer_norm_config = 3;
inline bool BackendConfig::_internal_has_onednn_layer_norm_config() const {
  return backend_config_oneof_case() == kOnednnLayerNormConfig;
}
inline bool BackendConfig::has_onednn_layer_norm_config() const {
  return _internal_has_onednn_layer_norm_config();
}
inline void BackendConfig::set_has_onednn_layer_norm_config() {
  _impl_._oneof_case_[0] = kOnednnLayerNormConfig;
}
inline ::xla::cpu::OneDnnNormConfig* BackendConfig::release_onednn_layer_norm_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.BackendConfig.onednn_layer_norm_config)
  if (_internal_has_onednn_layer_norm_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnNormConfig* temp = _impl_.backend_config_oneof_.onednn_layer_norm_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.backend_config_oneof_.onednn_layer_norm_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnNormConfig& BackendConfig::_internal_onednn_layer_norm_config() const {
  return _internal_has_onednn_layer_norm_config()
      ? *_impl_.backend_config_oneof_.onednn_layer_norm_config_
      : reinterpret_cast< ::xla::cpu::OneDnnNormConfig&>(::xla::cpu::_OneDnnNormConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnNormConfig& BackendConfig::onednn_layer_norm_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.onednn_layer_norm_config)
  return _internal_onednn_layer_norm_config();
}
inline ::xla::cpu::OneDnnNormConfig* BackendConfig::unsafe_arena_release_onednn_layer_norm_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.BackendConfig.onednn_layer_norm_config)
  if (_internal_has_onednn_layer_norm_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnNormConfig* temp = _impl_.backend_config_oneof_.onednn_layer_norm_config_;
    _impl_.backend_config_oneof_.onednn_layer_norm_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BackendConfig::unsafe_arena_set_allocated_onednn_layer_norm_config(::xla::cpu::OneDnnNormConfig* onednn_layer_norm_config) {
  clear_backend_config_oneof();
  if (onednn_layer_norm_config) {
    set_has_onednn_layer_norm_config();
    _impl_.backend_config_oneof_.onednn_layer_norm_config_ = onednn_layer_norm_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.BackendConfig.onednn_layer_norm_config)
}
inline ::xla::cpu::OneDnnNormConfig* BackendConfig::_internal_mutable_onednn_layer_norm_config() {
  if (!_internal_has_onednn_layer_norm_config()) {
    clear_backend_config_oneof();
    set_has_onednn_layer_norm_config();
    _impl_.backend_config_oneof_.onednn_layer_norm_config_ = CreateMaybeMessage< ::xla::cpu::OneDnnNormConfig >(GetArenaForAllocation());
  }
  return _impl_.backend_config_oneof_.onednn_layer_norm_config_;
}
inline ::xla::cpu::OneDnnNormConfig* BackendConfig::mutable_onednn_layer_norm_config() {
  ::xla::cpu::OneDnnNormConfig* _msg = _internal_mutable_onednn_layer_norm_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.BackendConfig.onednn_layer_norm_config)
  return _msg;
}

// .xla.cpu.OneDnnSoftmaxConfig onednn_softmax_config = 4;
inline bool BackendConfig::_internal_has_onednn_softmax_config() const {
  return backend_config_oneof_case() == kOnednnSoftmaxConfig;
}
inline bool BackendConfig::has_onednn_softmax_config() const {
  return _internal_has_onednn_softmax_config();
}
inline void BackendConfig::set_has_onednn_softmax_config() {
  _impl_._oneof_case_[0] = kOnednnSoftmaxConfig;
}
inline ::xla::cpu::OneDnnSoftmaxConfig* BackendConfig::release_onednn_softmax_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.BackendConfig.onednn_softmax_config)
  if (_internal_has_onednn_softmax_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnSoftmaxConfig* temp = _impl_.backend_config_oneof_.onednn_softmax_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.backend_config_oneof_.onednn_softmax_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnSoftmaxConfig& BackendConfig::_internal_onednn_softmax_config() const {
  return _internal_has_onednn_softmax_config()
      ? *_impl_.backend_config_oneof_.onednn_softmax_config_
      : reinterpret_cast< ::xla::cpu::OneDnnSoftmaxConfig&>(::xla::cpu::_OneDnnSoftmaxConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnSoftmaxConfig& BackendConfig::onednn_softmax_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.onednn_softmax_config)
  return _internal_onednn_softmax_config();
}
inline ::xla::cpu::OneDnnSoftmaxConfig* BackendConfig::unsafe_arena_release_onednn_softmax_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.BackendConfig.onednn_softmax_config)
  if (_internal_has_onednn_softmax_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnSoftmaxConfig* temp = _impl_.backend_config_oneof_.onednn_softmax_config_;
    _impl_.backend_config_oneof_.onednn_softmax_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BackendConfig::unsafe_arena_set_allocated_onednn_softmax_config(::xla::cpu::OneDnnSoftmaxConfig* onednn_softmax_config) {
  clear_backend_config_oneof();
  if (onednn_softmax_config) {
    set_has_onednn_softmax_config();
    _impl_.backend_config_oneof_.onednn_softmax_config_ = onednn_softmax_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.BackendConfig.onednn_softmax_config)
}
inline ::xla::cpu::OneDnnSoftmaxConfig* BackendConfig::_internal_mutable_onednn_softmax_config() {
  if (!_internal_has_onednn_softmax_config()) {
    clear_backend_config_oneof();
    set_has_onednn_softmax_config();
    _impl_.backend_config_oneof_.onednn_softmax_config_ = CreateMaybeMessage< ::xla::cpu::OneDnnSoftmaxConfig >(GetArenaForAllocation());
  }
  return _impl_.backend_config_oneof_.onednn_softmax_config_;
}
inline ::xla::cpu::OneDnnSoftmaxConfig* BackendConfig::mutable_onednn_softmax_config() {
  ::xla::cpu::OneDnnSoftmaxConfig* _msg = _internal_mutable_onednn_softmax_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.BackendConfig.onednn_softmax_config)
  return _msg;
}

// .xla.cpu.OneDnnConvolutionConfig onednn_conv_config = 5;
inline bool BackendConfig::_internal_has_onednn_conv_config() const {
  return backend_config_oneof_case() == kOnednnConvConfig;
}
inline bool BackendConfig::has_onednn_conv_config() const {
  return _internal_has_onednn_conv_config();
}
inline void BackendConfig::set_has_onednn_conv_config() {
  _impl_._oneof_case_[0] = kOnednnConvConfig;
}
inline ::xla::cpu::OneDnnConvolutionConfig* BackendConfig::release_onednn_conv_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.BackendConfig.onednn_conv_config)
  if (_internal_has_onednn_conv_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnConvolutionConfig* temp = _impl_.backend_config_oneof_.onednn_conv_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.backend_config_oneof_.onednn_conv_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnConvolutionConfig& BackendConfig::_internal_onednn_conv_config() const {
  return _internal_has_onednn_conv_config()
      ? *_impl_.backend_config_oneof_.onednn_conv_config_
      : reinterpret_cast< ::xla::cpu::OneDnnConvolutionConfig&>(::xla::cpu::_OneDnnConvolutionConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnConvolutionConfig& BackendConfig::onednn_conv_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.onednn_conv_config)
  return _internal_onednn_conv_config();
}
inline ::xla::cpu::OneDnnConvolutionConfig* BackendConfig::unsafe_arena_release_onednn_conv_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.BackendConfig.onednn_conv_config)
  if (_internal_has_onednn_conv_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::OneDnnConvolutionConfig* temp = _impl_.backend_config_oneof_.onednn_conv_config_;
    _impl_.backend_config_oneof_.onednn_conv_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BackendConfig::unsafe_arena_set_allocated_onednn_conv_config(::xla::cpu::OneDnnConvolutionConfig* onednn_conv_config) {
  clear_backend_config_oneof();
  if (onednn_conv_config) {
    set_has_onednn_conv_config();
    _impl_.backend_config_oneof_.onednn_conv_config_ = onednn_conv_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.BackendConfig.onednn_conv_config)
}
inline ::xla::cpu::OneDnnConvolutionConfig* BackendConfig::_internal_mutable_onednn_conv_config() {
  if (!_internal_has_onednn_conv_config()) {
    clear_backend_config_oneof();
    set_has_onednn_conv_config();
    _impl_.backend_config_oneof_.onednn_conv_config_ = CreateMaybeMessage< ::xla::cpu::OneDnnConvolutionConfig >(GetArenaForAllocation());
  }
  return _impl_.backend_config_oneof_.onednn_conv_config_;
}
inline ::xla::cpu::OneDnnConvolutionConfig* BackendConfig::mutable_onednn_conv_config() {
  ::xla::cpu::OneDnnConvolutionConfig* _msg = _internal_mutable_onednn_conv_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.BackendConfig.onednn_conv_config)
  return _msg;
}

// .xla.cpu.CustomCallBackendConfig custom_call_config = 6;
inline bool BackendConfig::_internal_has_custom_call_config() const {
  return backend_config_oneof_case() == kCustomCallConfig;
}
inline bool BackendConfig::has_custom_call_config() const {
  return _internal_has_custom_call_config();
}
inline void BackendConfig::set_has_custom_call_config() {
  _impl_._oneof_case_[0] = kCustomCallConfig;
}
inline void BackendConfig::clear_custom_call_config() {
  if (_internal_has_custom_call_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.backend_config_oneof_.custom_call_config_;
    }
    clear_has_backend_config_oneof();
  }
}
inline ::xla::cpu::CustomCallBackendConfig* BackendConfig::release_custom_call_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.BackendConfig.custom_call_config)
  if (_internal_has_custom_call_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::CustomCallBackendConfig* temp = _impl_.backend_config_oneof_.custom_call_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.backend_config_oneof_.custom_call_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::CustomCallBackendConfig& BackendConfig::_internal_custom_call_config() const {
  return _internal_has_custom_call_config()
      ? *_impl_.backend_config_oneof_.custom_call_config_
      : reinterpret_cast< ::xla::cpu::CustomCallBackendConfig&>(::xla::cpu::_CustomCallBackendConfig_default_instance_);
}
inline const ::xla::cpu::CustomCallBackendConfig& BackendConfig::custom_call_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.custom_call_config)
  return _internal_custom_call_config();
}
inline ::xla::cpu::CustomCallBackendConfig* BackendConfig::unsafe_arena_release_custom_call_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.BackendConfig.custom_call_config)
  if (_internal_has_custom_call_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::CustomCallBackendConfig* temp = _impl_.backend_config_oneof_.custom_call_config_;
    _impl_.backend_config_oneof_.custom_call_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BackendConfig::unsafe_arena_set_allocated_custom_call_config(::xla::cpu::CustomCallBackendConfig* custom_call_config) {
  clear_backend_config_oneof();
  if (custom_call_config) {
    set_has_custom_call_config();
    _impl_.backend_config_oneof_.custom_call_config_ = custom_call_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.BackendConfig.custom_call_config)
}
inline ::xla::cpu::CustomCallBackendConfig* BackendConfig::_internal_mutable_custom_call_config() {
  if (!_internal_has_custom_call_config()) {
    clear_backend_config_oneof();
    set_has_custom_call_config();
    _impl_.backend_config_oneof_.custom_call_config_ = CreateMaybeMessage< ::xla::cpu::CustomCallBackendConfig >(GetArenaForAllocation());
  }
  return _impl_.backend_config_oneof_.custom_call_config_;
}
inline ::xla::cpu::CustomCallBackendConfig* BackendConfig::mutable_custom_call_config() {
  ::xla::cpu::CustomCallBackendConfig* _msg = _internal_mutable_custom_call_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.BackendConfig.custom_call_config)
  return _msg;
}

// .xla.cpu.FusionBackendConfig fusion_config = 7;
inline bool BackendConfig::_internal_has_fusion_config() const {
  return backend_config_oneof_case() == kFusionConfig;
}
inline bool BackendConfig::has_fusion_config() const {
  return _internal_has_fusion_config();
}
inline void BackendConfig::set_has_fusion_config() {
  _impl_._oneof_case_[0] = kFusionConfig;
}
inline void BackendConfig::clear_fusion_config() {
  if (_internal_has_fusion_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.backend_config_oneof_.fusion_config_;
    }
    clear_has_backend_config_oneof();
  }
}
inline ::xla::cpu::FusionBackendConfig* BackendConfig::release_fusion_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.BackendConfig.fusion_config)
  if (_internal_has_fusion_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::FusionBackendConfig* temp = _impl_.backend_config_oneof_.fusion_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.backend_config_oneof_.fusion_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::FusionBackendConfig& BackendConfig::_internal_fusion_config() const {
  return _internal_has_fusion_config()
      ? *_impl_.backend_config_oneof_.fusion_config_
      : reinterpret_cast< ::xla::cpu::FusionBackendConfig&>(::xla::cpu::_FusionBackendConfig_default_instance_);
}
inline const ::xla::cpu::FusionBackendConfig& BackendConfig::fusion_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.BackendConfig.fusion_config)
  return _internal_fusion_config();
}
inline ::xla::cpu::FusionBackendConfig* BackendConfig::unsafe_arena_release_fusion_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.BackendConfig.fusion_config)
  if (_internal_has_fusion_config()) {
    clear_has_backend_config_oneof();
    ::xla::cpu::FusionBackendConfig* temp = _impl_.backend_config_oneof_.fusion_config_;
    _impl_.backend_config_oneof_.fusion_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BackendConfig::unsafe_arena_set_allocated_fusion_config(::xla::cpu::FusionBackendConfig* fusion_config) {
  clear_backend_config_oneof();
  if (fusion_config) {
    set_has_fusion_config();
    _impl_.backend_config_oneof_.fusion_config_ = fusion_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.BackendConfig.fusion_config)
}
inline ::xla::cpu::FusionBackendConfig* BackendConfig::_internal_mutable_fusion_config() {
  if (!_internal_has_fusion_config()) {
    clear_backend_config_oneof();
    set_has_fusion_config();
    _impl_.backend_config_oneof_.fusion_config_ = CreateMaybeMessage< ::xla::cpu::FusionBackendConfig >(GetArenaForAllocation());
  }
  return _impl_.backend_config_oneof_.fusion_config_;
}
inline ::xla::cpu::FusionBackendConfig* BackendConfig::mutable_fusion_config() {
  ::xla::cpu::FusionBackendConfig* _msg = _internal_mutable_fusion_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.BackendConfig.fusion_config)
  return _msg;
}

inline bool BackendConfig::has_backend_config_oneof() const {
  return backend_config_oneof_case() != BACKEND_CONFIG_ONEOF_NOT_SET;
}
inline void BackendConfig::clear_has_backend_config_oneof() {
  _impl_._oneof_case_[0] = BACKEND_CONFIG_ONEOF_NOT_SET;
}
inline BackendConfig::BackendConfigOneofCase BackendConfig::backend_config_oneof_case() const {
  return BackendConfig::BackendConfigOneofCase(_impl_._oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace cpu
}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fbackend_5fconfig_2eproto
