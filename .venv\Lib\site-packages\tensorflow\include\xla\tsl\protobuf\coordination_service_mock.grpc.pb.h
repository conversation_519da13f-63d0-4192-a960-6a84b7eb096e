// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: xla/tsl/protobuf/coordination_service.proto

#include "xla/tsl/protobuf/coordination_service.pb.h"
#include "xla/tsl/protobuf/coordination_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace tensorflow {


namespace grpc {

class MockCoordinationServiceStub : public CoordinationService::StubInterface {
 public:
  MOCK_METHOD3(RegisterTask, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::tensorflow::RegisterTaskResponse* response));
  MOCK_METHOD3(AsyncRegisterTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncRegisterTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Heartbeat, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::tensorflow::HeartbeatResponse* response));
  MOCK_METHOD3(AsyncHeartbeatRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>*(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncHeartbeatRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>*(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(WaitForAllTasks, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::tensorflow::WaitForAllTasksResponse* response));
  MOCK_METHOD3(AsyncWaitForAllTasksRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>*(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncWaitForAllTasksRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>*(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(ShutdownTask, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::tensorflow::ShutdownTaskResponse* response));
  MOCK_METHOD3(AsyncShutdownTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncShutdownTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(ResetTask, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::tensorflow::ResetTaskResponse* response));
  MOCK_METHOD3(AsyncResetTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncResetTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(ReportErrorToTask, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::tensorflow::ReportErrorToTaskResponse* response));
  MOCK_METHOD3(AsyncReportErrorToTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncReportErrorToTaskRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>*(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(ReportErrorToService, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::tensorflow::ReportErrorToServiceResponse* response));
  MOCK_METHOD3(AsyncReportErrorToServiceRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>*(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncReportErrorToServiceRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>*(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetTaskState, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::tensorflow::GetTaskStateResponse* response));
  MOCK_METHOD3(AsyncGetTaskStateRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetTaskStateRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(InsertKeyValue, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::tensorflow::InsertKeyValueResponse* response));
  MOCK_METHOD3(AsyncInsertKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncInsertKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetKeyValue, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::tensorflow::GetKeyValueResponse* response));
  MOCK_METHOD3(AsyncGetKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(TryGetKeyValue, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::tensorflow::TryGetKeyValueResponse* response));
  MOCK_METHOD3(AsyncTryGetKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncTryGetKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetKeyValueDir, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::tensorflow::GetKeyValueDirResponse* response));
  MOCK_METHOD3(AsyncGetKeyValueDirRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetKeyValueDirRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(DeleteKeyValue, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::tensorflow::DeleteKeyValueResponse* response));
  MOCK_METHOD3(AsyncDeleteKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncDeleteKeyValueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>*(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Barrier, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::tensorflow::BarrierResponse* response));
  MOCK_METHOD3(AsyncBarrierRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>*(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncBarrierRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>*(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(CancelBarrier, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::tensorflow::CancelBarrierResponse* response));
  MOCK_METHOD3(AsyncCancelBarrierRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>*(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncCancelBarrierRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>*(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetAliveTasks, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::tensorflow::GetAliveTasksResponse* response));
  MOCK_METHOD3(AsyncGetAliveTasksRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetAliveTasksRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>*(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PollForError, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::tensorflow::PollForErrorResponse* response));
  MOCK_METHOD3(AsyncPollForErrorRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>*(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncPollForErrorRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>*(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc

} // namespace tensorflow

