// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/stream_executor/device_description.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "xla/autotune_results.pb.h"
#include "xla/stream_executor/cuda/cuda_compute_capability.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto;
namespace stream_executor {
class DnnVersionInfoProto;
struct DnnVersionInfoProtoDefaultTypeInternal;
extern DnnVersionInfoProtoDefaultTypeInternal _DnnVersionInfoProto_default_instance_;
class GpuDeviceInfoProto;
struct GpuDeviceInfoProtoDefaultTypeInternal;
extern GpuDeviceInfoProtoDefaultTypeInternal _GpuDeviceInfoProto_default_instance_;
class GpuTargetConfigProto;
struct GpuTargetConfigProtoDefaultTypeInternal;
extern GpuTargetConfigProtoDefaultTypeInternal _GpuTargetConfigProto_default_instance_;
class RocmComputeCapabilityProto;
struct RocmComputeCapabilityProtoDefaultTypeInternal;
extern RocmComputeCapabilityProtoDefaultTypeInternal _RocmComputeCapabilityProto_default_instance_;
}  // namespace stream_executor
PROTOBUF_NAMESPACE_OPEN
template<> ::stream_executor::DnnVersionInfoProto* Arena::CreateMaybeMessage<::stream_executor::DnnVersionInfoProto>(Arena*);
template<> ::stream_executor::GpuDeviceInfoProto* Arena::CreateMaybeMessage<::stream_executor::GpuDeviceInfoProto>(Arena*);
template<> ::stream_executor::GpuTargetConfigProto* Arena::CreateMaybeMessage<::stream_executor::GpuTargetConfigProto>(Arena*);
template<> ::stream_executor::RocmComputeCapabilityProto* Arena::CreateMaybeMessage<::stream_executor::RocmComputeCapabilityProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace stream_executor {

// ===================================================================

class RocmComputeCapabilityProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.RocmComputeCapabilityProto) */ {
 public:
  inline RocmComputeCapabilityProto() : RocmComputeCapabilityProto(nullptr) {}
  ~RocmComputeCapabilityProto() override;
  explicit PROTOBUF_CONSTEXPR RocmComputeCapabilityProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RocmComputeCapabilityProto(const RocmComputeCapabilityProto& from);
  RocmComputeCapabilityProto(RocmComputeCapabilityProto&& from) noexcept
    : RocmComputeCapabilityProto() {
    *this = ::std::move(from);
  }

  inline RocmComputeCapabilityProto& operator=(const RocmComputeCapabilityProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline RocmComputeCapabilityProto& operator=(RocmComputeCapabilityProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RocmComputeCapabilityProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const RocmComputeCapabilityProto* internal_default_instance() {
    return reinterpret_cast<const RocmComputeCapabilityProto*>(
               &_RocmComputeCapabilityProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RocmComputeCapabilityProto& a, RocmComputeCapabilityProto& b) {
    a.Swap(&b);
  }
  inline void Swap(RocmComputeCapabilityProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RocmComputeCapabilityProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RocmComputeCapabilityProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RocmComputeCapabilityProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RocmComputeCapabilityProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RocmComputeCapabilityProto& from) {
    RocmComputeCapabilityProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RocmComputeCapabilityProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.RocmComputeCapabilityProto";
  }
  protected:
  explicit RocmComputeCapabilityProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGcnArchNameFieldNumber = 1,
  };
  // string gcn_arch_name = 1;
  void clear_gcn_arch_name();
  const std::string& gcn_arch_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gcn_arch_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gcn_arch_name();
  PROTOBUF_NODISCARD std::string* release_gcn_arch_name();
  void set_allocated_gcn_arch_name(std::string* gcn_arch_name);
  private:
  const std::string& _internal_gcn_arch_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gcn_arch_name(const std::string& value);
  std::string* _internal_mutable_gcn_arch_name();
  public:

  // @@protoc_insertion_point(class_scope:stream_executor.RocmComputeCapabilityProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gcn_arch_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto;
};
// -------------------------------------------------------------------

class GpuDeviceInfoProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.GpuDeviceInfoProto) */ {
 public:
  inline GpuDeviceInfoProto() : GpuDeviceInfoProto(nullptr) {}
  ~GpuDeviceInfoProto() override;
  explicit PROTOBUF_CONSTEXPR GpuDeviceInfoProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GpuDeviceInfoProto(const GpuDeviceInfoProto& from);
  GpuDeviceInfoProto(GpuDeviceInfoProto&& from) noexcept
    : GpuDeviceInfoProto() {
    *this = ::std::move(from);
  }

  inline GpuDeviceInfoProto& operator=(const GpuDeviceInfoProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GpuDeviceInfoProto& operator=(GpuDeviceInfoProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GpuDeviceInfoProto& default_instance() {
    return *internal_default_instance();
  }
  enum ComputeCapabilityCase {
    kCudaComputeCapability = 16,
    kRocmComputeCapability = 17,
    COMPUTE_CAPABILITY_NOT_SET = 0,
  };

  static inline const GpuDeviceInfoProto* internal_default_instance() {
    return reinterpret_cast<const GpuDeviceInfoProto*>(
               &_GpuDeviceInfoProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GpuDeviceInfoProto& a, GpuDeviceInfoProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GpuDeviceInfoProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GpuDeviceInfoProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GpuDeviceInfoProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GpuDeviceInfoProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GpuDeviceInfoProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GpuDeviceInfoProto& from) {
    GpuDeviceInfoProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GpuDeviceInfoProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.GpuDeviceInfoProto";
  }
  protected:
  explicit GpuDeviceInfoProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kThreadsPerBlockLimitFieldNumber = 1,
    kThreadsPerWarpFieldNumber = 2,
    kSharedMemoryPerBlockFieldNumber = 3,
    kSharedMemoryPerCoreFieldNumber = 4,
    kThreadsPerCoreLimitFieldNumber = 5,
    kCoreCountFieldNumber = 6,
    kFpusPerCoreFieldNumber = 7,
    kBlockDimLimitXFieldNumber = 8,
    kBlockDimLimitYFieldNumber = 9,
    kMemoryBandwidthFieldNumber = 11,
    kBlockDimLimitZFieldNumber = 10,
    kClockRateGhzFieldNumber = 13,
    kL2CacheSizeFieldNumber = 12,
    kDeviceMemorySizeFieldNumber = 14,
    kRegistersPerCoreLimitFieldNumber = 18,
    kRegistersPerBlockLimitFieldNumber = 19,
    kSharedMemoryPerBlockOptinFieldNumber = 15,
    kCudaComputeCapabilityFieldNumber = 16,
    kRocmComputeCapabilityFieldNumber = 17,
  };
  // int32 threads_per_block_limit = 1;
  void clear_threads_per_block_limit();
  int32_t threads_per_block_limit() const;
  void set_threads_per_block_limit(int32_t value);
  private:
  int32_t _internal_threads_per_block_limit() const;
  void _internal_set_threads_per_block_limit(int32_t value);
  public:

  // int32 threads_per_warp = 2;
  void clear_threads_per_warp();
  int32_t threads_per_warp() const;
  void set_threads_per_warp(int32_t value);
  private:
  int32_t _internal_threads_per_warp() const;
  void _internal_set_threads_per_warp(int32_t value);
  public:

  // int32 shared_memory_per_block = 3;
  void clear_shared_memory_per_block();
  int32_t shared_memory_per_block() const;
  void set_shared_memory_per_block(int32_t value);
  private:
  int32_t _internal_shared_memory_per_block() const;
  void _internal_set_shared_memory_per_block(int32_t value);
  public:

  // int32 shared_memory_per_core = 4;
  void clear_shared_memory_per_core();
  int32_t shared_memory_per_core() const;
  void set_shared_memory_per_core(int32_t value);
  private:
  int32_t _internal_shared_memory_per_core() const;
  void _internal_set_shared_memory_per_core(int32_t value);
  public:

  // int32 threads_per_core_limit = 5;
  void clear_threads_per_core_limit();
  int32_t threads_per_core_limit() const;
  void set_threads_per_core_limit(int32_t value);
  private:
  int32_t _internal_threads_per_core_limit() const;
  void _internal_set_threads_per_core_limit(int32_t value);
  public:

  // int32 core_count = 6;
  void clear_core_count();
  int32_t core_count() const;
  void set_core_count(int32_t value);
  private:
  int32_t _internal_core_count() const;
  void _internal_set_core_count(int32_t value);
  public:

  // int64 fpus_per_core = 7;
  void clear_fpus_per_core();
  int64_t fpus_per_core() const;
  void set_fpus_per_core(int64_t value);
  private:
  int64_t _internal_fpus_per_core() const;
  void _internal_set_fpus_per_core(int64_t value);
  public:

  // int32 block_dim_limit_x = 8;
  void clear_block_dim_limit_x();
  int32_t block_dim_limit_x() const;
  void set_block_dim_limit_x(int32_t value);
  private:
  int32_t _internal_block_dim_limit_x() const;
  void _internal_set_block_dim_limit_x(int32_t value);
  public:

  // int32 block_dim_limit_y = 9;
  void clear_block_dim_limit_y();
  int32_t block_dim_limit_y() const;
  void set_block_dim_limit_y(int32_t value);
  private:
  int32_t _internal_block_dim_limit_y() const;
  void _internal_set_block_dim_limit_y(int32_t value);
  public:

  // int64 memory_bandwidth = 11;
  void clear_memory_bandwidth();
  int64_t memory_bandwidth() const;
  void set_memory_bandwidth(int64_t value);
  private:
  int64_t _internal_memory_bandwidth() const;
  void _internal_set_memory_bandwidth(int64_t value);
  public:

  // int32 block_dim_limit_z = 10;
  void clear_block_dim_limit_z();
  int32_t block_dim_limit_z() const;
  void set_block_dim_limit_z(int32_t value);
  private:
  int32_t _internal_block_dim_limit_z() const;
  void _internal_set_block_dim_limit_z(int32_t value);
  public:

  // float clock_rate_ghz = 13;
  void clear_clock_rate_ghz();
  float clock_rate_ghz() const;
  void set_clock_rate_ghz(float value);
  private:
  float _internal_clock_rate_ghz() const;
  void _internal_set_clock_rate_ghz(float value);
  public:

  // int64 l2_cache_size = 12;
  void clear_l2_cache_size();
  int64_t l2_cache_size() const;
  void set_l2_cache_size(int64_t value);
  private:
  int64_t _internal_l2_cache_size() const;
  void _internal_set_l2_cache_size(int64_t value);
  public:

  // int64 device_memory_size = 14;
  void clear_device_memory_size();
  int64_t device_memory_size() const;
  void set_device_memory_size(int64_t value);
  private:
  int64_t _internal_device_memory_size() const;
  void _internal_set_device_memory_size(int64_t value);
  public:

  // int64 registers_per_core_limit = 18;
  void clear_registers_per_core_limit();
  int64_t registers_per_core_limit() const;
  void set_registers_per_core_limit(int64_t value);
  private:
  int64_t _internal_registers_per_core_limit() const;
  void _internal_set_registers_per_core_limit(int64_t value);
  public:

  // int64 registers_per_block_limit = 19;
  void clear_registers_per_block_limit();
  int64_t registers_per_block_limit() const;
  void set_registers_per_block_limit(int64_t value);
  private:
  int64_t _internal_registers_per_block_limit() const;
  void _internal_set_registers_per_block_limit(int64_t value);
  public:

  // int32 shared_memory_per_block_optin = 15;
  void clear_shared_memory_per_block_optin();
  int32_t shared_memory_per_block_optin() const;
  void set_shared_memory_per_block_optin(int32_t value);
  private:
  int32_t _internal_shared_memory_per_block_optin() const;
  void _internal_set_shared_memory_per_block_optin(int32_t value);
  public:

  // .stream_executor.CudaComputeCapabilityProto cuda_compute_capability = 16;
  bool has_cuda_compute_capability() const;
  private:
  bool _internal_has_cuda_compute_capability() const;
  public:
  void clear_cuda_compute_capability();
  const ::stream_executor::CudaComputeCapabilityProto& cuda_compute_capability() const;
  PROTOBUF_NODISCARD ::stream_executor::CudaComputeCapabilityProto* release_cuda_compute_capability();
  ::stream_executor::CudaComputeCapabilityProto* mutable_cuda_compute_capability();
  void set_allocated_cuda_compute_capability(::stream_executor::CudaComputeCapabilityProto* cuda_compute_capability);
  private:
  const ::stream_executor::CudaComputeCapabilityProto& _internal_cuda_compute_capability() const;
  ::stream_executor::CudaComputeCapabilityProto* _internal_mutable_cuda_compute_capability();
  public:
  void unsafe_arena_set_allocated_cuda_compute_capability(
      ::stream_executor::CudaComputeCapabilityProto* cuda_compute_capability);
  ::stream_executor::CudaComputeCapabilityProto* unsafe_arena_release_cuda_compute_capability();

  // .stream_executor.RocmComputeCapabilityProto rocm_compute_capability = 17;
  bool has_rocm_compute_capability() const;
  private:
  bool _internal_has_rocm_compute_capability() const;
  public:
  void clear_rocm_compute_capability();
  const ::stream_executor::RocmComputeCapabilityProto& rocm_compute_capability() const;
  PROTOBUF_NODISCARD ::stream_executor::RocmComputeCapabilityProto* release_rocm_compute_capability();
  ::stream_executor::RocmComputeCapabilityProto* mutable_rocm_compute_capability();
  void set_allocated_rocm_compute_capability(::stream_executor::RocmComputeCapabilityProto* rocm_compute_capability);
  private:
  const ::stream_executor::RocmComputeCapabilityProto& _internal_rocm_compute_capability() const;
  ::stream_executor::RocmComputeCapabilityProto* _internal_mutable_rocm_compute_capability();
  public:
  void unsafe_arena_set_allocated_rocm_compute_capability(
      ::stream_executor::RocmComputeCapabilityProto* rocm_compute_capability);
  ::stream_executor::RocmComputeCapabilityProto* unsafe_arena_release_rocm_compute_capability();

  void clear_compute_capability();
  ComputeCapabilityCase compute_capability_case() const;
  // @@protoc_insertion_point(class_scope:stream_executor.GpuDeviceInfoProto)
 private:
  class _Internal;
  void set_has_cuda_compute_capability();
  void set_has_rocm_compute_capability();

  inline bool has_compute_capability() const;
  inline void clear_has_compute_capability();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t threads_per_block_limit_;
    int32_t threads_per_warp_;
    int32_t shared_memory_per_block_;
    int32_t shared_memory_per_core_;
    int32_t threads_per_core_limit_;
    int32_t core_count_;
    int64_t fpus_per_core_;
    int32_t block_dim_limit_x_;
    int32_t block_dim_limit_y_;
    int64_t memory_bandwidth_;
    int32_t block_dim_limit_z_;
    float clock_rate_ghz_;
    int64_t l2_cache_size_;
    int64_t device_memory_size_;
    int64_t registers_per_core_limit_;
    int64_t registers_per_block_limit_;
    int32_t shared_memory_per_block_optin_;
    union ComputeCapabilityUnion {
      constexpr ComputeCapabilityUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stream_executor::CudaComputeCapabilityProto* cuda_compute_capability_;
      ::stream_executor::RocmComputeCapabilityProto* rocm_compute_capability_;
    } compute_capability_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto;
};
// -------------------------------------------------------------------

class DnnVersionInfoProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.DnnVersionInfoProto) */ {
 public:
  inline DnnVersionInfoProto() : DnnVersionInfoProto(nullptr) {}
  ~DnnVersionInfoProto() override;
  explicit PROTOBUF_CONSTEXPR DnnVersionInfoProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DnnVersionInfoProto(const DnnVersionInfoProto& from);
  DnnVersionInfoProto(DnnVersionInfoProto&& from) noexcept
    : DnnVersionInfoProto() {
    *this = ::std::move(from);
  }

  inline DnnVersionInfoProto& operator=(const DnnVersionInfoProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline DnnVersionInfoProto& operator=(DnnVersionInfoProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DnnVersionInfoProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const DnnVersionInfoProto* internal_default_instance() {
    return reinterpret_cast<const DnnVersionInfoProto*>(
               &_DnnVersionInfoProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DnnVersionInfoProto& a, DnnVersionInfoProto& b) {
    a.Swap(&b);
  }
  inline void Swap(DnnVersionInfoProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DnnVersionInfoProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DnnVersionInfoProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DnnVersionInfoProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DnnVersionInfoProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DnnVersionInfoProto& from) {
    DnnVersionInfoProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DnnVersionInfoProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.DnnVersionInfoProto";
  }
  protected:
  explicit DnnVersionInfoProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMajorFieldNumber = 1,
    kMinorFieldNumber = 2,
    kPatchFieldNumber = 3,
  };
  // int32 major = 1;
  void clear_major();
  int32_t major() const;
  void set_major(int32_t value);
  private:
  int32_t _internal_major() const;
  void _internal_set_major(int32_t value);
  public:

  // int32 minor = 2;
  void clear_minor();
  int32_t minor() const;
  void set_minor(int32_t value);
  private:
  int32_t _internal_minor() const;
  void _internal_set_minor(int32_t value);
  public:

  // int32 patch = 3;
  void clear_patch();
  int32_t patch() const;
  void set_patch(int32_t value);
  private:
  int32_t _internal_patch() const;
  void _internal_set_patch(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:stream_executor.DnnVersionInfoProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t major_;
    int32_t minor_;
    int32_t patch_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto;
};
// -------------------------------------------------------------------

class GpuTargetConfigProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.GpuTargetConfigProto) */ {
 public:
  inline GpuTargetConfigProto() : GpuTargetConfigProto(nullptr) {}
  ~GpuTargetConfigProto() override;
  explicit PROTOBUF_CONSTEXPR GpuTargetConfigProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GpuTargetConfigProto(const GpuTargetConfigProto& from);
  GpuTargetConfigProto(GpuTargetConfigProto&& from) noexcept
    : GpuTargetConfigProto() {
    *this = ::std::move(from);
  }

  inline GpuTargetConfigProto& operator=(const GpuTargetConfigProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GpuTargetConfigProto& operator=(GpuTargetConfigProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GpuTargetConfigProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const GpuTargetConfigProto* internal_default_instance() {
    return reinterpret_cast<const GpuTargetConfigProto*>(
               &_GpuTargetConfigProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GpuTargetConfigProto& a, GpuTargetConfigProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GpuTargetConfigProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GpuTargetConfigProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GpuTargetConfigProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GpuTargetConfigProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GpuTargetConfigProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GpuTargetConfigProto& from) {
    GpuTargetConfigProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GpuTargetConfigProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.GpuTargetConfigProto";
  }
  protected:
  explicit GpuTargetConfigProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlatformNameFieldNumber = 4,
    kDeviceDescriptionStrFieldNumber = 7,
    kGpuDeviceInfoFieldNumber = 1,
    kDnnVersionInfoFieldNumber = 5,
    kAutotuneResultsFieldNumber = 6,
  };
  // string platform_name = 4;
  void clear_platform_name();
  const std::string& platform_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_platform_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_platform_name();
  PROTOBUF_NODISCARD std::string* release_platform_name();
  void set_allocated_platform_name(std::string* platform_name);
  private:
  const std::string& _internal_platform_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_platform_name(const std::string& value);
  std::string* _internal_mutable_platform_name();
  public:

  // string device_description_str = 7;
  void clear_device_description_str();
  const std::string& device_description_str() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_description_str(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_description_str();
  PROTOBUF_NODISCARD std::string* release_device_description_str();
  void set_allocated_device_description_str(std::string* device_description_str);
  private:
  const std::string& _internal_device_description_str() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_description_str(const std::string& value);
  std::string* _internal_mutable_device_description_str();
  public:

  // .stream_executor.GpuDeviceInfoProto gpu_device_info = 1;
  bool has_gpu_device_info() const;
  private:
  bool _internal_has_gpu_device_info() const;
  public:
  void clear_gpu_device_info();
  const ::stream_executor::GpuDeviceInfoProto& gpu_device_info() const;
  PROTOBUF_NODISCARD ::stream_executor::GpuDeviceInfoProto* release_gpu_device_info();
  ::stream_executor::GpuDeviceInfoProto* mutable_gpu_device_info();
  void set_allocated_gpu_device_info(::stream_executor::GpuDeviceInfoProto* gpu_device_info);
  private:
  const ::stream_executor::GpuDeviceInfoProto& _internal_gpu_device_info() const;
  ::stream_executor::GpuDeviceInfoProto* _internal_mutable_gpu_device_info();
  public:
  void unsafe_arena_set_allocated_gpu_device_info(
      ::stream_executor::GpuDeviceInfoProto* gpu_device_info);
  ::stream_executor::GpuDeviceInfoProto* unsafe_arena_release_gpu_device_info();

  // .stream_executor.DnnVersionInfoProto dnn_version_info = 5;
  bool has_dnn_version_info() const;
  private:
  bool _internal_has_dnn_version_info() const;
  public:
  void clear_dnn_version_info();
  const ::stream_executor::DnnVersionInfoProto& dnn_version_info() const;
  PROTOBUF_NODISCARD ::stream_executor::DnnVersionInfoProto* release_dnn_version_info();
  ::stream_executor::DnnVersionInfoProto* mutable_dnn_version_info();
  void set_allocated_dnn_version_info(::stream_executor::DnnVersionInfoProto* dnn_version_info);
  private:
  const ::stream_executor::DnnVersionInfoProto& _internal_dnn_version_info() const;
  ::stream_executor::DnnVersionInfoProto* _internal_mutable_dnn_version_info();
  public:
  void unsafe_arena_set_allocated_dnn_version_info(
      ::stream_executor::DnnVersionInfoProto* dnn_version_info);
  ::stream_executor::DnnVersionInfoProto* unsafe_arena_release_dnn_version_info();

  // .xla.AutotuneResults autotune_results = 6;
  bool has_autotune_results() const;
  private:
  bool _internal_has_autotune_results() const;
  public:
  void clear_autotune_results();
  const ::xla::AutotuneResults& autotune_results() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResults* release_autotune_results();
  ::xla::AutotuneResults* mutable_autotune_results();
  void set_allocated_autotune_results(::xla::AutotuneResults* autotune_results);
  private:
  const ::xla::AutotuneResults& _internal_autotune_results() const;
  ::xla::AutotuneResults* _internal_mutable_autotune_results();
  public:
  void unsafe_arena_set_allocated_autotune_results(
      ::xla::AutotuneResults* autotune_results);
  ::xla::AutotuneResults* unsafe_arena_release_autotune_results();

  // @@protoc_insertion_point(class_scope:stream_executor.GpuTargetConfigProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr platform_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_description_str_;
    ::stream_executor::GpuDeviceInfoProto* gpu_device_info_;
    ::stream_executor::DnnVersionInfoProto* dnn_version_info_;
    ::xla::AutotuneResults* autotune_results_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RocmComputeCapabilityProto

// string gcn_arch_name = 1;
inline void RocmComputeCapabilityProto::clear_gcn_arch_name() {
  _impl_.gcn_arch_name_.ClearToEmpty();
}
inline const std::string& RocmComputeCapabilityProto::gcn_arch_name() const {
  // @@protoc_insertion_point(field_get:stream_executor.RocmComputeCapabilityProto.gcn_arch_name)
  return _internal_gcn_arch_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RocmComputeCapabilityProto::set_gcn_arch_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.gcn_arch_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stream_executor.RocmComputeCapabilityProto.gcn_arch_name)
}
inline std::string* RocmComputeCapabilityProto::mutable_gcn_arch_name() {
  std::string* _s = _internal_mutable_gcn_arch_name();
  // @@protoc_insertion_point(field_mutable:stream_executor.RocmComputeCapabilityProto.gcn_arch_name)
  return _s;
}
inline const std::string& RocmComputeCapabilityProto::_internal_gcn_arch_name() const {
  return _impl_.gcn_arch_name_.Get();
}
inline void RocmComputeCapabilityProto::_internal_set_gcn_arch_name(const std::string& value) {
  
  _impl_.gcn_arch_name_.Set(value, GetArenaForAllocation());
}
inline std::string* RocmComputeCapabilityProto::_internal_mutable_gcn_arch_name() {
  
  return _impl_.gcn_arch_name_.Mutable(GetArenaForAllocation());
}
inline std::string* RocmComputeCapabilityProto::release_gcn_arch_name() {
  // @@protoc_insertion_point(field_release:stream_executor.RocmComputeCapabilityProto.gcn_arch_name)
  return _impl_.gcn_arch_name_.Release();
}
inline void RocmComputeCapabilityProto::set_allocated_gcn_arch_name(std::string* gcn_arch_name) {
  if (gcn_arch_name != nullptr) {
    
  } else {
    
  }
  _impl_.gcn_arch_name_.SetAllocated(gcn_arch_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.gcn_arch_name_.IsDefault()) {
    _impl_.gcn_arch_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stream_executor.RocmComputeCapabilityProto.gcn_arch_name)
}

// -------------------------------------------------------------------

// GpuDeviceInfoProto

// int32 threads_per_block_limit = 1;
inline void GpuDeviceInfoProto::clear_threads_per_block_limit() {
  _impl_.threads_per_block_limit_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_threads_per_block_limit() const {
  return _impl_.threads_per_block_limit_;
}
inline int32_t GpuDeviceInfoProto::threads_per_block_limit() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.threads_per_block_limit)
  return _internal_threads_per_block_limit();
}
inline void GpuDeviceInfoProto::_internal_set_threads_per_block_limit(int32_t value) {
  
  _impl_.threads_per_block_limit_ = value;
}
inline void GpuDeviceInfoProto::set_threads_per_block_limit(int32_t value) {
  _internal_set_threads_per_block_limit(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.threads_per_block_limit)
}

// int32 threads_per_warp = 2;
inline void GpuDeviceInfoProto::clear_threads_per_warp() {
  _impl_.threads_per_warp_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_threads_per_warp() const {
  return _impl_.threads_per_warp_;
}
inline int32_t GpuDeviceInfoProto::threads_per_warp() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.threads_per_warp)
  return _internal_threads_per_warp();
}
inline void GpuDeviceInfoProto::_internal_set_threads_per_warp(int32_t value) {
  
  _impl_.threads_per_warp_ = value;
}
inline void GpuDeviceInfoProto::set_threads_per_warp(int32_t value) {
  _internal_set_threads_per_warp(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.threads_per_warp)
}

// int32 shared_memory_per_block = 3;
inline void GpuDeviceInfoProto::clear_shared_memory_per_block() {
  _impl_.shared_memory_per_block_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_shared_memory_per_block() const {
  return _impl_.shared_memory_per_block_;
}
inline int32_t GpuDeviceInfoProto::shared_memory_per_block() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.shared_memory_per_block)
  return _internal_shared_memory_per_block();
}
inline void GpuDeviceInfoProto::_internal_set_shared_memory_per_block(int32_t value) {
  
  _impl_.shared_memory_per_block_ = value;
}
inline void GpuDeviceInfoProto::set_shared_memory_per_block(int32_t value) {
  _internal_set_shared_memory_per_block(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.shared_memory_per_block)
}

// int32 shared_memory_per_core = 4;
inline void GpuDeviceInfoProto::clear_shared_memory_per_core() {
  _impl_.shared_memory_per_core_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_shared_memory_per_core() const {
  return _impl_.shared_memory_per_core_;
}
inline int32_t GpuDeviceInfoProto::shared_memory_per_core() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.shared_memory_per_core)
  return _internal_shared_memory_per_core();
}
inline void GpuDeviceInfoProto::_internal_set_shared_memory_per_core(int32_t value) {
  
  _impl_.shared_memory_per_core_ = value;
}
inline void GpuDeviceInfoProto::set_shared_memory_per_core(int32_t value) {
  _internal_set_shared_memory_per_core(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.shared_memory_per_core)
}

// int32 threads_per_core_limit = 5;
inline void GpuDeviceInfoProto::clear_threads_per_core_limit() {
  _impl_.threads_per_core_limit_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_threads_per_core_limit() const {
  return _impl_.threads_per_core_limit_;
}
inline int32_t GpuDeviceInfoProto::threads_per_core_limit() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.threads_per_core_limit)
  return _internal_threads_per_core_limit();
}
inline void GpuDeviceInfoProto::_internal_set_threads_per_core_limit(int32_t value) {
  
  _impl_.threads_per_core_limit_ = value;
}
inline void GpuDeviceInfoProto::set_threads_per_core_limit(int32_t value) {
  _internal_set_threads_per_core_limit(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.threads_per_core_limit)
}

// int32 core_count = 6;
inline void GpuDeviceInfoProto::clear_core_count() {
  _impl_.core_count_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_core_count() const {
  return _impl_.core_count_;
}
inline int32_t GpuDeviceInfoProto::core_count() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.core_count)
  return _internal_core_count();
}
inline void GpuDeviceInfoProto::_internal_set_core_count(int32_t value) {
  
  _impl_.core_count_ = value;
}
inline void GpuDeviceInfoProto::set_core_count(int32_t value) {
  _internal_set_core_count(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.core_count)
}

// int64 fpus_per_core = 7;
inline void GpuDeviceInfoProto::clear_fpus_per_core() {
  _impl_.fpus_per_core_ = int64_t{0};
}
inline int64_t GpuDeviceInfoProto::_internal_fpus_per_core() const {
  return _impl_.fpus_per_core_;
}
inline int64_t GpuDeviceInfoProto::fpus_per_core() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.fpus_per_core)
  return _internal_fpus_per_core();
}
inline void GpuDeviceInfoProto::_internal_set_fpus_per_core(int64_t value) {
  
  _impl_.fpus_per_core_ = value;
}
inline void GpuDeviceInfoProto::set_fpus_per_core(int64_t value) {
  _internal_set_fpus_per_core(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.fpus_per_core)
}

// int32 block_dim_limit_x = 8;
inline void GpuDeviceInfoProto::clear_block_dim_limit_x() {
  _impl_.block_dim_limit_x_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_block_dim_limit_x() const {
  return _impl_.block_dim_limit_x_;
}
inline int32_t GpuDeviceInfoProto::block_dim_limit_x() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.block_dim_limit_x)
  return _internal_block_dim_limit_x();
}
inline void GpuDeviceInfoProto::_internal_set_block_dim_limit_x(int32_t value) {
  
  _impl_.block_dim_limit_x_ = value;
}
inline void GpuDeviceInfoProto::set_block_dim_limit_x(int32_t value) {
  _internal_set_block_dim_limit_x(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.block_dim_limit_x)
}

// int32 block_dim_limit_y = 9;
inline void GpuDeviceInfoProto::clear_block_dim_limit_y() {
  _impl_.block_dim_limit_y_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_block_dim_limit_y() const {
  return _impl_.block_dim_limit_y_;
}
inline int32_t GpuDeviceInfoProto::block_dim_limit_y() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.block_dim_limit_y)
  return _internal_block_dim_limit_y();
}
inline void GpuDeviceInfoProto::_internal_set_block_dim_limit_y(int32_t value) {
  
  _impl_.block_dim_limit_y_ = value;
}
inline void GpuDeviceInfoProto::set_block_dim_limit_y(int32_t value) {
  _internal_set_block_dim_limit_y(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.block_dim_limit_y)
}

// int32 block_dim_limit_z = 10;
inline void GpuDeviceInfoProto::clear_block_dim_limit_z() {
  _impl_.block_dim_limit_z_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_block_dim_limit_z() const {
  return _impl_.block_dim_limit_z_;
}
inline int32_t GpuDeviceInfoProto::block_dim_limit_z() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.block_dim_limit_z)
  return _internal_block_dim_limit_z();
}
inline void GpuDeviceInfoProto::_internal_set_block_dim_limit_z(int32_t value) {
  
  _impl_.block_dim_limit_z_ = value;
}
inline void GpuDeviceInfoProto::set_block_dim_limit_z(int32_t value) {
  _internal_set_block_dim_limit_z(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.block_dim_limit_z)
}

// int64 memory_bandwidth = 11;
inline void GpuDeviceInfoProto::clear_memory_bandwidth() {
  _impl_.memory_bandwidth_ = int64_t{0};
}
inline int64_t GpuDeviceInfoProto::_internal_memory_bandwidth() const {
  return _impl_.memory_bandwidth_;
}
inline int64_t GpuDeviceInfoProto::memory_bandwidth() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.memory_bandwidth)
  return _internal_memory_bandwidth();
}
inline void GpuDeviceInfoProto::_internal_set_memory_bandwidth(int64_t value) {
  
  _impl_.memory_bandwidth_ = value;
}
inline void GpuDeviceInfoProto::set_memory_bandwidth(int64_t value) {
  _internal_set_memory_bandwidth(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.memory_bandwidth)
}

// int64 l2_cache_size = 12;
inline void GpuDeviceInfoProto::clear_l2_cache_size() {
  _impl_.l2_cache_size_ = int64_t{0};
}
inline int64_t GpuDeviceInfoProto::_internal_l2_cache_size() const {
  return _impl_.l2_cache_size_;
}
inline int64_t GpuDeviceInfoProto::l2_cache_size() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.l2_cache_size)
  return _internal_l2_cache_size();
}
inline void GpuDeviceInfoProto::_internal_set_l2_cache_size(int64_t value) {
  
  _impl_.l2_cache_size_ = value;
}
inline void GpuDeviceInfoProto::set_l2_cache_size(int64_t value) {
  _internal_set_l2_cache_size(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.l2_cache_size)
}

// float clock_rate_ghz = 13;
inline void GpuDeviceInfoProto::clear_clock_rate_ghz() {
  _impl_.clock_rate_ghz_ = 0;
}
inline float GpuDeviceInfoProto::_internal_clock_rate_ghz() const {
  return _impl_.clock_rate_ghz_;
}
inline float GpuDeviceInfoProto::clock_rate_ghz() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.clock_rate_ghz)
  return _internal_clock_rate_ghz();
}
inline void GpuDeviceInfoProto::_internal_set_clock_rate_ghz(float value) {
  
  _impl_.clock_rate_ghz_ = value;
}
inline void GpuDeviceInfoProto::set_clock_rate_ghz(float value) {
  _internal_set_clock_rate_ghz(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.clock_rate_ghz)
}

// int64 device_memory_size = 14;
inline void GpuDeviceInfoProto::clear_device_memory_size() {
  _impl_.device_memory_size_ = int64_t{0};
}
inline int64_t GpuDeviceInfoProto::_internal_device_memory_size() const {
  return _impl_.device_memory_size_;
}
inline int64_t GpuDeviceInfoProto::device_memory_size() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.device_memory_size)
  return _internal_device_memory_size();
}
inline void GpuDeviceInfoProto::_internal_set_device_memory_size(int64_t value) {
  
  _impl_.device_memory_size_ = value;
}
inline void GpuDeviceInfoProto::set_device_memory_size(int64_t value) {
  _internal_set_device_memory_size(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.device_memory_size)
}

// int32 shared_memory_per_block_optin = 15;
inline void GpuDeviceInfoProto::clear_shared_memory_per_block_optin() {
  _impl_.shared_memory_per_block_optin_ = 0;
}
inline int32_t GpuDeviceInfoProto::_internal_shared_memory_per_block_optin() const {
  return _impl_.shared_memory_per_block_optin_;
}
inline int32_t GpuDeviceInfoProto::shared_memory_per_block_optin() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.shared_memory_per_block_optin)
  return _internal_shared_memory_per_block_optin();
}
inline void GpuDeviceInfoProto::_internal_set_shared_memory_per_block_optin(int32_t value) {
  
  _impl_.shared_memory_per_block_optin_ = value;
}
inline void GpuDeviceInfoProto::set_shared_memory_per_block_optin(int32_t value) {
  _internal_set_shared_memory_per_block_optin(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.shared_memory_per_block_optin)
}

// .stream_executor.CudaComputeCapabilityProto cuda_compute_capability = 16;
inline bool GpuDeviceInfoProto::_internal_has_cuda_compute_capability() const {
  return compute_capability_case() == kCudaComputeCapability;
}
inline bool GpuDeviceInfoProto::has_cuda_compute_capability() const {
  return _internal_has_cuda_compute_capability();
}
inline void GpuDeviceInfoProto::set_has_cuda_compute_capability() {
  _impl_._oneof_case_[0] = kCudaComputeCapability;
}
inline ::stream_executor::CudaComputeCapabilityProto* GpuDeviceInfoProto::release_cuda_compute_capability() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuDeviceInfoProto.cuda_compute_capability)
  if (_internal_has_cuda_compute_capability()) {
    clear_has_compute_capability();
    ::stream_executor::CudaComputeCapabilityProto* temp = _impl_.compute_capability_.cuda_compute_capability_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.compute_capability_.cuda_compute_capability_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stream_executor::CudaComputeCapabilityProto& GpuDeviceInfoProto::_internal_cuda_compute_capability() const {
  return _internal_has_cuda_compute_capability()
      ? *_impl_.compute_capability_.cuda_compute_capability_
      : reinterpret_cast< ::stream_executor::CudaComputeCapabilityProto&>(::stream_executor::_CudaComputeCapabilityProto_default_instance_);
}
inline const ::stream_executor::CudaComputeCapabilityProto& GpuDeviceInfoProto::cuda_compute_capability() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.cuda_compute_capability)
  return _internal_cuda_compute_capability();
}
inline ::stream_executor::CudaComputeCapabilityProto* GpuDeviceInfoProto::unsafe_arena_release_cuda_compute_capability() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stream_executor.GpuDeviceInfoProto.cuda_compute_capability)
  if (_internal_has_cuda_compute_capability()) {
    clear_has_compute_capability();
    ::stream_executor::CudaComputeCapabilityProto* temp = _impl_.compute_capability_.cuda_compute_capability_;
    _impl_.compute_capability_.cuda_compute_capability_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GpuDeviceInfoProto::unsafe_arena_set_allocated_cuda_compute_capability(::stream_executor::CudaComputeCapabilityProto* cuda_compute_capability) {
  clear_compute_capability();
  if (cuda_compute_capability) {
    set_has_cuda_compute_capability();
    _impl_.compute_capability_.cuda_compute_capability_ = cuda_compute_capability;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.GpuDeviceInfoProto.cuda_compute_capability)
}
inline ::stream_executor::CudaComputeCapabilityProto* GpuDeviceInfoProto::_internal_mutable_cuda_compute_capability() {
  if (!_internal_has_cuda_compute_capability()) {
    clear_compute_capability();
    set_has_cuda_compute_capability();
    _impl_.compute_capability_.cuda_compute_capability_ = CreateMaybeMessage< ::stream_executor::CudaComputeCapabilityProto >(GetArenaForAllocation());
  }
  return _impl_.compute_capability_.cuda_compute_capability_;
}
inline ::stream_executor::CudaComputeCapabilityProto* GpuDeviceInfoProto::mutable_cuda_compute_capability() {
  ::stream_executor::CudaComputeCapabilityProto* _msg = _internal_mutable_cuda_compute_capability();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuDeviceInfoProto.cuda_compute_capability)
  return _msg;
}

// .stream_executor.RocmComputeCapabilityProto rocm_compute_capability = 17;
inline bool GpuDeviceInfoProto::_internal_has_rocm_compute_capability() const {
  return compute_capability_case() == kRocmComputeCapability;
}
inline bool GpuDeviceInfoProto::has_rocm_compute_capability() const {
  return _internal_has_rocm_compute_capability();
}
inline void GpuDeviceInfoProto::set_has_rocm_compute_capability() {
  _impl_._oneof_case_[0] = kRocmComputeCapability;
}
inline void GpuDeviceInfoProto::clear_rocm_compute_capability() {
  if (_internal_has_rocm_compute_capability()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.compute_capability_.rocm_compute_capability_;
    }
    clear_has_compute_capability();
  }
}
inline ::stream_executor::RocmComputeCapabilityProto* GpuDeviceInfoProto::release_rocm_compute_capability() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuDeviceInfoProto.rocm_compute_capability)
  if (_internal_has_rocm_compute_capability()) {
    clear_has_compute_capability();
    ::stream_executor::RocmComputeCapabilityProto* temp = _impl_.compute_capability_.rocm_compute_capability_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.compute_capability_.rocm_compute_capability_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stream_executor::RocmComputeCapabilityProto& GpuDeviceInfoProto::_internal_rocm_compute_capability() const {
  return _internal_has_rocm_compute_capability()
      ? *_impl_.compute_capability_.rocm_compute_capability_
      : reinterpret_cast< ::stream_executor::RocmComputeCapabilityProto&>(::stream_executor::_RocmComputeCapabilityProto_default_instance_);
}
inline const ::stream_executor::RocmComputeCapabilityProto& GpuDeviceInfoProto::rocm_compute_capability() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.rocm_compute_capability)
  return _internal_rocm_compute_capability();
}
inline ::stream_executor::RocmComputeCapabilityProto* GpuDeviceInfoProto::unsafe_arena_release_rocm_compute_capability() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stream_executor.GpuDeviceInfoProto.rocm_compute_capability)
  if (_internal_has_rocm_compute_capability()) {
    clear_has_compute_capability();
    ::stream_executor::RocmComputeCapabilityProto* temp = _impl_.compute_capability_.rocm_compute_capability_;
    _impl_.compute_capability_.rocm_compute_capability_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GpuDeviceInfoProto::unsafe_arena_set_allocated_rocm_compute_capability(::stream_executor::RocmComputeCapabilityProto* rocm_compute_capability) {
  clear_compute_capability();
  if (rocm_compute_capability) {
    set_has_rocm_compute_capability();
    _impl_.compute_capability_.rocm_compute_capability_ = rocm_compute_capability;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.GpuDeviceInfoProto.rocm_compute_capability)
}
inline ::stream_executor::RocmComputeCapabilityProto* GpuDeviceInfoProto::_internal_mutable_rocm_compute_capability() {
  if (!_internal_has_rocm_compute_capability()) {
    clear_compute_capability();
    set_has_rocm_compute_capability();
    _impl_.compute_capability_.rocm_compute_capability_ = CreateMaybeMessage< ::stream_executor::RocmComputeCapabilityProto >(GetArenaForAllocation());
  }
  return _impl_.compute_capability_.rocm_compute_capability_;
}
inline ::stream_executor::RocmComputeCapabilityProto* GpuDeviceInfoProto::mutable_rocm_compute_capability() {
  ::stream_executor::RocmComputeCapabilityProto* _msg = _internal_mutable_rocm_compute_capability();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuDeviceInfoProto.rocm_compute_capability)
  return _msg;
}

// int64 registers_per_core_limit = 18;
inline void GpuDeviceInfoProto::clear_registers_per_core_limit() {
  _impl_.registers_per_core_limit_ = int64_t{0};
}
inline int64_t GpuDeviceInfoProto::_internal_registers_per_core_limit() const {
  return _impl_.registers_per_core_limit_;
}
inline int64_t GpuDeviceInfoProto::registers_per_core_limit() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.registers_per_core_limit)
  return _internal_registers_per_core_limit();
}
inline void GpuDeviceInfoProto::_internal_set_registers_per_core_limit(int64_t value) {
  
  _impl_.registers_per_core_limit_ = value;
}
inline void GpuDeviceInfoProto::set_registers_per_core_limit(int64_t value) {
  _internal_set_registers_per_core_limit(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.registers_per_core_limit)
}

// int64 registers_per_block_limit = 19;
inline void GpuDeviceInfoProto::clear_registers_per_block_limit() {
  _impl_.registers_per_block_limit_ = int64_t{0};
}
inline int64_t GpuDeviceInfoProto::_internal_registers_per_block_limit() const {
  return _impl_.registers_per_block_limit_;
}
inline int64_t GpuDeviceInfoProto::registers_per_block_limit() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuDeviceInfoProto.registers_per_block_limit)
  return _internal_registers_per_block_limit();
}
inline void GpuDeviceInfoProto::_internal_set_registers_per_block_limit(int64_t value) {
  
  _impl_.registers_per_block_limit_ = value;
}
inline void GpuDeviceInfoProto::set_registers_per_block_limit(int64_t value) {
  _internal_set_registers_per_block_limit(value);
  // @@protoc_insertion_point(field_set:stream_executor.GpuDeviceInfoProto.registers_per_block_limit)
}

inline bool GpuDeviceInfoProto::has_compute_capability() const {
  return compute_capability_case() != COMPUTE_CAPABILITY_NOT_SET;
}
inline void GpuDeviceInfoProto::clear_has_compute_capability() {
  _impl_._oneof_case_[0] = COMPUTE_CAPABILITY_NOT_SET;
}
inline GpuDeviceInfoProto::ComputeCapabilityCase GpuDeviceInfoProto::compute_capability_case() const {
  return GpuDeviceInfoProto::ComputeCapabilityCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// DnnVersionInfoProto

// int32 major = 1;
inline void DnnVersionInfoProto::clear_major() {
  _impl_.major_ = 0;
}
inline int32_t DnnVersionInfoProto::_internal_major() const {
  return _impl_.major_;
}
inline int32_t DnnVersionInfoProto::major() const {
  // @@protoc_insertion_point(field_get:stream_executor.DnnVersionInfoProto.major)
  return _internal_major();
}
inline void DnnVersionInfoProto::_internal_set_major(int32_t value) {
  
  _impl_.major_ = value;
}
inline void DnnVersionInfoProto::set_major(int32_t value) {
  _internal_set_major(value);
  // @@protoc_insertion_point(field_set:stream_executor.DnnVersionInfoProto.major)
}

// int32 minor = 2;
inline void DnnVersionInfoProto::clear_minor() {
  _impl_.minor_ = 0;
}
inline int32_t DnnVersionInfoProto::_internal_minor() const {
  return _impl_.minor_;
}
inline int32_t DnnVersionInfoProto::minor() const {
  // @@protoc_insertion_point(field_get:stream_executor.DnnVersionInfoProto.minor)
  return _internal_minor();
}
inline void DnnVersionInfoProto::_internal_set_minor(int32_t value) {
  
  _impl_.minor_ = value;
}
inline void DnnVersionInfoProto::set_minor(int32_t value) {
  _internal_set_minor(value);
  // @@protoc_insertion_point(field_set:stream_executor.DnnVersionInfoProto.minor)
}

// int32 patch = 3;
inline void DnnVersionInfoProto::clear_patch() {
  _impl_.patch_ = 0;
}
inline int32_t DnnVersionInfoProto::_internal_patch() const {
  return _impl_.patch_;
}
inline int32_t DnnVersionInfoProto::patch() const {
  // @@protoc_insertion_point(field_get:stream_executor.DnnVersionInfoProto.patch)
  return _internal_patch();
}
inline void DnnVersionInfoProto::_internal_set_patch(int32_t value) {
  
  _impl_.patch_ = value;
}
inline void DnnVersionInfoProto::set_patch(int32_t value) {
  _internal_set_patch(value);
  // @@protoc_insertion_point(field_set:stream_executor.DnnVersionInfoProto.patch)
}

// -------------------------------------------------------------------

// GpuTargetConfigProto

// .stream_executor.GpuDeviceInfoProto gpu_device_info = 1;
inline bool GpuTargetConfigProto::_internal_has_gpu_device_info() const {
  return this != internal_default_instance() && _impl_.gpu_device_info_ != nullptr;
}
inline bool GpuTargetConfigProto::has_gpu_device_info() const {
  return _internal_has_gpu_device_info();
}
inline void GpuTargetConfigProto::clear_gpu_device_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.gpu_device_info_ != nullptr) {
    delete _impl_.gpu_device_info_;
  }
  _impl_.gpu_device_info_ = nullptr;
}
inline const ::stream_executor::GpuDeviceInfoProto& GpuTargetConfigProto::_internal_gpu_device_info() const {
  const ::stream_executor::GpuDeviceInfoProto* p = _impl_.gpu_device_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::GpuDeviceInfoProto&>(
      ::stream_executor::_GpuDeviceInfoProto_default_instance_);
}
inline const ::stream_executor::GpuDeviceInfoProto& GpuTargetConfigProto::gpu_device_info() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuTargetConfigProto.gpu_device_info)
  return _internal_gpu_device_info();
}
inline void GpuTargetConfigProto::unsafe_arena_set_allocated_gpu_device_info(
    ::stream_executor::GpuDeviceInfoProto* gpu_device_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.gpu_device_info_);
  }
  _impl_.gpu_device_info_ = gpu_device_info;
  if (gpu_device_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.GpuTargetConfigProto.gpu_device_info)
}
inline ::stream_executor::GpuDeviceInfoProto* GpuTargetConfigProto::release_gpu_device_info() {
  
  ::stream_executor::GpuDeviceInfoProto* temp = _impl_.gpu_device_info_;
  _impl_.gpu_device_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::GpuDeviceInfoProto* GpuTargetConfigProto::unsafe_arena_release_gpu_device_info() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuTargetConfigProto.gpu_device_info)
  
  ::stream_executor::GpuDeviceInfoProto* temp = _impl_.gpu_device_info_;
  _impl_.gpu_device_info_ = nullptr;
  return temp;
}
inline ::stream_executor::GpuDeviceInfoProto* GpuTargetConfigProto::_internal_mutable_gpu_device_info() {
  
  if (_impl_.gpu_device_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::GpuDeviceInfoProto>(GetArenaForAllocation());
    _impl_.gpu_device_info_ = p;
  }
  return _impl_.gpu_device_info_;
}
inline ::stream_executor::GpuDeviceInfoProto* GpuTargetConfigProto::mutable_gpu_device_info() {
  ::stream_executor::GpuDeviceInfoProto* _msg = _internal_mutable_gpu_device_info();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuTargetConfigProto.gpu_device_info)
  return _msg;
}
inline void GpuTargetConfigProto::set_allocated_gpu_device_info(::stream_executor::GpuDeviceInfoProto* gpu_device_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.gpu_device_info_;
  }
  if (gpu_device_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(gpu_device_info);
    if (message_arena != submessage_arena) {
      gpu_device_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gpu_device_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.gpu_device_info_ = gpu_device_info;
  // @@protoc_insertion_point(field_set_allocated:stream_executor.GpuTargetConfigProto.gpu_device_info)
}

// string platform_name = 4;
inline void GpuTargetConfigProto::clear_platform_name() {
  _impl_.platform_name_.ClearToEmpty();
}
inline const std::string& GpuTargetConfigProto::platform_name() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuTargetConfigProto.platform_name)
  return _internal_platform_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GpuTargetConfigProto::set_platform_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.platform_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stream_executor.GpuTargetConfigProto.platform_name)
}
inline std::string* GpuTargetConfigProto::mutable_platform_name() {
  std::string* _s = _internal_mutable_platform_name();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuTargetConfigProto.platform_name)
  return _s;
}
inline const std::string& GpuTargetConfigProto::_internal_platform_name() const {
  return _impl_.platform_name_.Get();
}
inline void GpuTargetConfigProto::_internal_set_platform_name(const std::string& value) {
  
  _impl_.platform_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GpuTargetConfigProto::_internal_mutable_platform_name() {
  
  return _impl_.platform_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GpuTargetConfigProto::release_platform_name() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuTargetConfigProto.platform_name)
  return _impl_.platform_name_.Release();
}
inline void GpuTargetConfigProto::set_allocated_platform_name(std::string* platform_name) {
  if (platform_name != nullptr) {
    
  } else {
    
  }
  _impl_.platform_name_.SetAllocated(platform_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.platform_name_.IsDefault()) {
    _impl_.platform_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stream_executor.GpuTargetConfigProto.platform_name)
}

// .stream_executor.DnnVersionInfoProto dnn_version_info = 5;
inline bool GpuTargetConfigProto::_internal_has_dnn_version_info() const {
  return this != internal_default_instance() && _impl_.dnn_version_info_ != nullptr;
}
inline bool GpuTargetConfigProto::has_dnn_version_info() const {
  return _internal_has_dnn_version_info();
}
inline void GpuTargetConfigProto::clear_dnn_version_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.dnn_version_info_ != nullptr) {
    delete _impl_.dnn_version_info_;
  }
  _impl_.dnn_version_info_ = nullptr;
}
inline const ::stream_executor::DnnVersionInfoProto& GpuTargetConfigProto::_internal_dnn_version_info() const {
  const ::stream_executor::DnnVersionInfoProto* p = _impl_.dnn_version_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::DnnVersionInfoProto&>(
      ::stream_executor::_DnnVersionInfoProto_default_instance_);
}
inline const ::stream_executor::DnnVersionInfoProto& GpuTargetConfigProto::dnn_version_info() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuTargetConfigProto.dnn_version_info)
  return _internal_dnn_version_info();
}
inline void GpuTargetConfigProto::unsafe_arena_set_allocated_dnn_version_info(
    ::stream_executor::DnnVersionInfoProto* dnn_version_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.dnn_version_info_);
  }
  _impl_.dnn_version_info_ = dnn_version_info;
  if (dnn_version_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.GpuTargetConfigProto.dnn_version_info)
}
inline ::stream_executor::DnnVersionInfoProto* GpuTargetConfigProto::release_dnn_version_info() {
  
  ::stream_executor::DnnVersionInfoProto* temp = _impl_.dnn_version_info_;
  _impl_.dnn_version_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::DnnVersionInfoProto* GpuTargetConfigProto::unsafe_arena_release_dnn_version_info() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuTargetConfigProto.dnn_version_info)
  
  ::stream_executor::DnnVersionInfoProto* temp = _impl_.dnn_version_info_;
  _impl_.dnn_version_info_ = nullptr;
  return temp;
}
inline ::stream_executor::DnnVersionInfoProto* GpuTargetConfigProto::_internal_mutable_dnn_version_info() {
  
  if (_impl_.dnn_version_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::DnnVersionInfoProto>(GetArenaForAllocation());
    _impl_.dnn_version_info_ = p;
  }
  return _impl_.dnn_version_info_;
}
inline ::stream_executor::DnnVersionInfoProto* GpuTargetConfigProto::mutable_dnn_version_info() {
  ::stream_executor::DnnVersionInfoProto* _msg = _internal_mutable_dnn_version_info();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuTargetConfigProto.dnn_version_info)
  return _msg;
}
inline void GpuTargetConfigProto::set_allocated_dnn_version_info(::stream_executor::DnnVersionInfoProto* dnn_version_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.dnn_version_info_;
  }
  if (dnn_version_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dnn_version_info);
    if (message_arena != submessage_arena) {
      dnn_version_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dnn_version_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.dnn_version_info_ = dnn_version_info;
  // @@protoc_insertion_point(field_set_allocated:stream_executor.GpuTargetConfigProto.dnn_version_info)
}

// .xla.AutotuneResults autotune_results = 6;
inline bool GpuTargetConfigProto::_internal_has_autotune_results() const {
  return this != internal_default_instance() && _impl_.autotune_results_ != nullptr;
}
inline bool GpuTargetConfigProto::has_autotune_results() const {
  return _internal_has_autotune_results();
}
inline const ::xla::AutotuneResults& GpuTargetConfigProto::_internal_autotune_results() const {
  const ::xla::AutotuneResults* p = _impl_.autotune_results_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::AutotuneResults&>(
      ::xla::_AutotuneResults_default_instance_);
}
inline const ::xla::AutotuneResults& GpuTargetConfigProto::autotune_results() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuTargetConfigProto.autotune_results)
  return _internal_autotune_results();
}
inline void GpuTargetConfigProto::unsafe_arena_set_allocated_autotune_results(
    ::xla::AutotuneResults* autotune_results) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.autotune_results_);
  }
  _impl_.autotune_results_ = autotune_results;
  if (autotune_results) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.GpuTargetConfigProto.autotune_results)
}
inline ::xla::AutotuneResults* GpuTargetConfigProto::release_autotune_results() {
  
  ::xla::AutotuneResults* temp = _impl_.autotune_results_;
  _impl_.autotune_results_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::AutotuneResults* GpuTargetConfigProto::unsafe_arena_release_autotune_results() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuTargetConfigProto.autotune_results)
  
  ::xla::AutotuneResults* temp = _impl_.autotune_results_;
  _impl_.autotune_results_ = nullptr;
  return temp;
}
inline ::xla::AutotuneResults* GpuTargetConfigProto::_internal_mutable_autotune_results() {
  
  if (_impl_.autotune_results_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::AutotuneResults>(GetArenaForAllocation());
    _impl_.autotune_results_ = p;
  }
  return _impl_.autotune_results_;
}
inline ::xla::AutotuneResults* GpuTargetConfigProto::mutable_autotune_results() {
  ::xla::AutotuneResults* _msg = _internal_mutable_autotune_results();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuTargetConfigProto.autotune_results)
  return _msg;
}
inline void GpuTargetConfigProto::set_allocated_autotune_results(::xla::AutotuneResults* autotune_results) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.autotune_results_);
  }
  if (autotune_results) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(autotune_results));
    if (message_arena != submessage_arena) {
      autotune_results = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, autotune_results, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.autotune_results_ = autotune_results;
  // @@protoc_insertion_point(field_set_allocated:stream_executor.GpuTargetConfigProto.autotune_results)
}

// string device_description_str = 7;
inline void GpuTargetConfigProto::clear_device_description_str() {
  _impl_.device_description_str_.ClearToEmpty();
}
inline const std::string& GpuTargetConfigProto::device_description_str() const {
  // @@protoc_insertion_point(field_get:stream_executor.GpuTargetConfigProto.device_description_str)
  return _internal_device_description_str();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GpuTargetConfigProto::set_device_description_str(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_description_str_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stream_executor.GpuTargetConfigProto.device_description_str)
}
inline std::string* GpuTargetConfigProto::mutable_device_description_str() {
  std::string* _s = _internal_mutable_device_description_str();
  // @@protoc_insertion_point(field_mutable:stream_executor.GpuTargetConfigProto.device_description_str)
  return _s;
}
inline const std::string& GpuTargetConfigProto::_internal_device_description_str() const {
  return _impl_.device_description_str_.Get();
}
inline void GpuTargetConfigProto::_internal_set_device_description_str(const std::string& value) {
  
  _impl_.device_description_str_.Set(value, GetArenaForAllocation());
}
inline std::string* GpuTargetConfigProto::_internal_mutable_device_description_str() {
  
  return _impl_.device_description_str_.Mutable(GetArenaForAllocation());
}
inline std::string* GpuTargetConfigProto::release_device_description_str() {
  // @@protoc_insertion_point(field_release:stream_executor.GpuTargetConfigProto.device_description_str)
  return _impl_.device_description_str_.Release();
}
inline void GpuTargetConfigProto::set_allocated_device_description_str(std::string* device_description_str) {
  if (device_description_str != nullptr) {
    
  } else {
    
  }
  _impl_.device_description_str_.SetAllocated(device_description_str, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_description_str_.IsDefault()) {
    _impl_.device_description_str_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stream_executor.GpuTargetConfigProto.device_description_str)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace stream_executor

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fstream_5fexecutor_2fdevice_5fdescription_2eproto
