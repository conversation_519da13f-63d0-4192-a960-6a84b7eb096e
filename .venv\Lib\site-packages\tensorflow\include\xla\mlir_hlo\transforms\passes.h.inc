/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ALLOCTOARGPASS
#define GEN_PASS_DECL_COLLAPSEPARALLELLOOPSTO1DPASS
#define GEN_PASS_DECL_COMPUTEOPANDFUNCBUFFERIZEPASS
#define GEN_PASS_DECL_DETENSORIZESCFOPSPASS
#define GEN_PASS_DECL_FINALBUFFERIZEPASS
#define GEN_PASS_DECL_GENERICHOSTTOLLVMPASS
#define GEN_PASS_DECL_LOWERINDEXCASTPASS
#define GEN_PASS_DECL_NAIVECOPYREMOVALPASS
#define GEN_PASS_DECL_ONESHOTBUFFERIZE
#define GEN_PASS_DECL_TILELOOPSPASS
#define GEN_PASS_DECL_UNBUFFERIZEPASS
#define GEN_PASS_DECL_VECTORIZECOPYPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AllocToArgPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ALLOCTOARGPASS
#undef GEN_PASS_DECL_ALLOCTOARGPASS
#endif // GEN_PASS_DECL_ALLOCTOARGPASS
#ifdef GEN_PASS_DEF_ALLOCTOARGPASS
namespace impl {

template <typename DerivedT>
class AllocToArgPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = AllocToArgPassBase;

  AllocToArgPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AllocToArgPassBase(const AllocToArgPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  AllocToArgPassBase& operator=(const AllocToArgPassBase &) = delete;
  AllocToArgPassBase(AllocToArgPassBase &&) = delete;
  AllocToArgPassBase& operator=(AllocToArgPassBase &&) = delete;
  ~AllocToArgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("alloc-to-arg");
  }
  ::llvm::StringRef getArgument() const override { return "alloc-to-arg"; }

  ::llvm::StringRef getDescription() const override { return "Hoist memref allocations to function arguments."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AllocToArgPass");
  }
  ::llvm::StringRef getName() const override { return "AllocToArgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AllocToArgPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ALLOCTOARGPASS
#endif // GEN_PASS_DEF_ALLOCTOARGPASS

//===----------------------------------------------------------------------===//
// CollapseParallelLoopsTo1DPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_COLLAPSEPARALLELLOOPSTO1DPASS
#undef GEN_PASS_DECL_COLLAPSEPARALLELLOOPSTO1DPASS
#endif // GEN_PASS_DECL_COLLAPSEPARALLELLOOPSTO1DPASS
#ifdef GEN_PASS_DEF_COLLAPSEPARALLELLOOPSTO1DPASS
namespace impl {

template <typename DerivedT>
class CollapseParallelLoopsTo1DPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CollapseParallelLoopsTo1DPassBase;

  CollapseParallelLoopsTo1DPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CollapseParallelLoopsTo1DPassBase(const CollapseParallelLoopsTo1DPassBase &other) : ::mlir::OperationPass<>(other) {}
  CollapseParallelLoopsTo1DPassBase& operator=(const CollapseParallelLoopsTo1DPassBase &) = delete;
  CollapseParallelLoopsTo1DPassBase(CollapseParallelLoopsTo1DPassBase &&) = delete;
  CollapseParallelLoopsTo1DPassBase& operator=(CollapseParallelLoopsTo1DPassBase &&) = delete;
  ~CollapseParallelLoopsTo1DPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("collapse-parallel-loops-to-1d");
  }
  ::llvm::StringRef getArgument() const override { return "collapse-parallel-loops-to-1d"; }

  ::llvm::StringRef getDescription() const override { return "Collapses multidimensional loops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CollapseParallelLoopsTo1DPass");
  }
  ::llvm::StringRef getName() const override { return "CollapseParallelLoopsTo1DPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CollapseParallelLoopsTo1DPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_COLLAPSEPARALLELLOOPSTO1DPASS
#endif // GEN_PASS_DEF_COLLAPSEPARALLELLOOPSTO1DPASS

//===----------------------------------------------------------------------===//
// ComputeOpAndFuncBufferizePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_COMPUTEOPANDFUNCBUFFERIZEPASS
#undef GEN_PASS_DECL_COMPUTEOPANDFUNCBUFFERIZEPASS
#endif // GEN_PASS_DECL_COMPUTEOPANDFUNCBUFFERIZEPASS
#ifdef GEN_PASS_DEF_COMPUTEOPANDFUNCBUFFERIZEPASS
namespace impl {

template <typename DerivedT>
class ComputeOpAndFuncBufferizePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ComputeOpAndFuncBufferizePassBase;

  ComputeOpAndFuncBufferizePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ComputeOpAndFuncBufferizePassBase(const ComputeOpAndFuncBufferizePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ComputeOpAndFuncBufferizePassBase& operator=(const ComputeOpAndFuncBufferizePassBase &) = delete;
  ComputeOpAndFuncBufferizePassBase(ComputeOpAndFuncBufferizePassBase &&) = delete;
  ComputeOpAndFuncBufferizePassBase& operator=(ComputeOpAndFuncBufferizePassBase &&) = delete;
  ~ComputeOpAndFuncBufferizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("computeop-and-func-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "computeop-and-func-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform compute operations (hlo and linalg) on values to buffer based ones."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ComputeOpAndFuncBufferizePass");
  }
  ::llvm::StringRef getName() const override { return "ComputeOpAndFuncBufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ComputeOpAndFuncBufferizePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_COMPUTEOPANDFUNCBUFFERIZEPASS
#endif // GEN_PASS_DEF_COMPUTEOPANDFUNCBUFFERIZEPASS

//===----------------------------------------------------------------------===//
// DetensorizeScfOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DETENSORIZESCFOPSPASS
#undef GEN_PASS_DECL_DETENSORIZESCFOPSPASS
#endif // GEN_PASS_DECL_DETENSORIZESCFOPSPASS
#ifdef GEN_PASS_DEF_DETENSORIZESCFOPSPASS
namespace impl {

template <typename DerivedT>
class DetensorizeScfOpsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = DetensorizeScfOpsPassBase;

  DetensorizeScfOpsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DetensorizeScfOpsPassBase(const DetensorizeScfOpsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  DetensorizeScfOpsPassBase& operator=(const DetensorizeScfOpsPassBase &) = delete;
  DetensorizeScfOpsPassBase(DetensorizeScfOpsPassBase &&) = delete;
  DetensorizeScfOpsPassBase& operator=(DetensorizeScfOpsPassBase &&) = delete;
  ~DetensorizeScfOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("detensorize-scf-ops");
  }
  ::llvm::StringRef getArgument() const override { return "detensorize-scf-ops"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize arguments of SCF ops where possible."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DetensorizeScfOpsPass");
  }
  ::llvm::StringRef getName() const override { return "DetensorizeScfOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DetensorizeScfOpsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DETENSORIZESCFOPSPASS
#endif // GEN_PASS_DEF_DETENSORIZESCFOPSPASS

//===----------------------------------------------------------------------===//
// FinalBufferizePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FINALBUFFERIZEPASS
struct FinalBufferizePassOptions {
  uint64_t alignment_ = 64;
};
#undef GEN_PASS_DECL_FINALBUFFERIZEPASS
#endif // GEN_PASS_DECL_FINALBUFFERIZEPASS
#ifdef GEN_PASS_DEF_FINALBUFFERIZEPASS
namespace impl {

template <typename DerivedT>
class FinalBufferizePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FinalBufferizePassBase;

  FinalBufferizePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalBufferizePassBase(const FinalBufferizePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FinalBufferizePassBase& operator=(const FinalBufferizePassBase &) = delete;
  FinalBufferizePassBase(FinalBufferizePassBase &&) = delete;
  FinalBufferizePassBase& operator=(FinalBufferizePassBase &&) = delete;
  ~FinalBufferizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("final-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "final-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform late operations on values to buffer based ones."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalBufferizePass");
  }
  ::llvm::StringRef getName() const override { return "FinalBufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalBufferizePassBase<DerivedT>)

  FinalBufferizePassBase(FinalBufferizePassOptions options) : FinalBufferizePassBase() {
    alignment_ = std::move(options.alignment_);
  }
protected:
  ::mlir::Pass::Option<uint64_t> alignment_{*this, "alignment", ::llvm::cl::desc("Memory alignment"), ::llvm::cl::init(64)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FINALBUFFERIZEPASS
#endif // GEN_PASS_DEF_FINALBUFFERIZEPASS

//===----------------------------------------------------------------------===//
// GenericHostToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GENERICHOSTTOLLVMPASS
struct GenericHostToLLVMPassOptions {
  bool enableAvx2 = false;
};
#undef GEN_PASS_DECL_GENERICHOSTTOLLVMPASS
#endif // GEN_PASS_DECL_GENERICHOSTTOLLVMPASS
#ifdef GEN_PASS_DEF_GENERICHOSTTOLLVMPASS
namespace impl {

template <typename DerivedT>
class GenericHostToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GenericHostToLLVMPassBase;

  GenericHostToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GenericHostToLLVMPassBase(const GenericHostToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  GenericHostToLLVMPassBase& operator=(const GenericHostToLLVMPassBase &) = delete;
  GenericHostToLLVMPassBase(GenericHostToLLVMPassBase &&) = delete;
  GenericHostToLLVMPassBase& operator=(GenericHostToLLVMPassBase &&) = delete;
  ~GenericHostToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("generic-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "generic-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Pass to lower common dialects resulting from HLO to LLVM."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GenericHostToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "GenericHostToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::LLVM::LLVMDialect>();
    registry.insert<::mlir::arith::ArithDialect>();
    registry.insert<::mlir::cf::ControlFlowDialect>();
    registry.insert<::mlir::complex::ComplexDialect>();
    registry.insert<::mlir::func::FuncDialect>();
    registry.insert<::mlir::math::MathDialect>();
    registry.insert<::mlir::memref::MemRefDialect>();
    registry.insert<::mlir::scf::SCFDialect>();
    registry.insert<::mlir::x86vector::X86VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GenericHostToLLVMPassBase<DerivedT>)

  GenericHostToLLVMPassBase(GenericHostToLLVMPassOptions options) : GenericHostToLLVMPassBase() {
    enableAvx2 = std::move(options.enableAvx2);
  }
protected:
  ::mlir::Pass::Option<bool> enableAvx2{*this, "enable-avx2", ::llvm::cl::desc("Whether to enable avx2"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GENERICHOSTTOLLVMPASS
#endif // GEN_PASS_DEF_GENERICHOSTTOLLVMPASS

//===----------------------------------------------------------------------===//
// LowerIndexCastPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERINDEXCASTPASS
#undef GEN_PASS_DECL_LOWERINDEXCASTPASS
#endif // GEN_PASS_DECL_LOWERINDEXCASTPASS
#ifdef GEN_PASS_DEF_LOWERINDEXCASTPASS
namespace impl {

template <typename DerivedT>
class LowerIndexCastPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LowerIndexCastPassBase;

  LowerIndexCastPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerIndexCastPassBase(const LowerIndexCastPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  LowerIndexCastPassBase& operator=(const LowerIndexCastPassBase &) = delete;
  LowerIndexCastPassBase(LowerIndexCastPassBase &&) = delete;
  LowerIndexCastPassBase& operator=(LowerIndexCastPassBase &&) = delete;
  ~LowerIndexCastPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-index-cast");
  }
  ::llvm::StringRef getArgument() const override { return "lower-index-cast"; }

  ::llvm::StringRef getDescription() const override { return "Lower index cast on tensors to tensor dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerIndexCastPass");
  }
  ::llvm::StringRef getName() const override { return "LowerIndexCastPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerIndexCastPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOWERINDEXCASTPASS
#endif // GEN_PASS_DEF_LOWERINDEXCASTPASS

//===----------------------------------------------------------------------===//
// NaiveCopyRemovalPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_NAIVECOPYREMOVALPASS
#undef GEN_PASS_DECL_NAIVECOPYREMOVALPASS
#endif // GEN_PASS_DECL_NAIVECOPYREMOVALPASS
#ifdef GEN_PASS_DEF_NAIVECOPYREMOVALPASS
namespace impl {

template <typename DerivedT>
class NaiveCopyRemovalPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = NaiveCopyRemovalPassBase;

  NaiveCopyRemovalPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  NaiveCopyRemovalPassBase(const NaiveCopyRemovalPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  NaiveCopyRemovalPassBase& operator=(const NaiveCopyRemovalPassBase &) = delete;
  NaiveCopyRemovalPassBase(NaiveCopyRemovalPassBase &&) = delete;
  NaiveCopyRemovalPassBase& operator=(NaiveCopyRemovalPassBase &&) = delete;
  ~NaiveCopyRemovalPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("naive-copy-removal");
  }
  ::llvm::StringRef getArgument() const override { return "naive-copy-removal"; }

  ::llvm::StringRef getDescription() const override { return "Pass to remove redundant `memref.copy` ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NaiveCopyRemovalPass");
  }
  ::llvm::StringRef getName() const override { return "NaiveCopyRemovalPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NaiveCopyRemovalPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_NAIVECOPYREMOVALPASS
#endif // GEN_PASS_DEF_NAIVECOPYREMOVALPASS

//===----------------------------------------------------------------------===//
// OneShotBufferize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ONESHOTBUFFERIZE
#undef GEN_PASS_DECL_ONESHOTBUFFERIZE
#endif // GEN_PASS_DECL_ONESHOTBUFFERIZE
#ifdef GEN_PASS_DEF_ONESHOTBUFFERIZE
namespace impl {

template <typename DerivedT>
class OneShotBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OneShotBufferizeBase;

  OneShotBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OneShotBufferizeBase(const OneShotBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  OneShotBufferizeBase& operator=(const OneShotBufferizeBase &) = delete;
  OneShotBufferizeBase(OneShotBufferizeBase &&) = delete;
  OneShotBufferizeBase& operator=(OneShotBufferizeBase &&) = delete;
  ~OneShotBufferizeBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-one-shot-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-one-shot-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "One shot bufferization pass."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OneShotBufferize");
  }
  ::llvm::StringRef getName() const override { return "OneShotBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OneShotBufferizeBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ONESHOTBUFFERIZE
#endif // GEN_PASS_DEF_ONESHOTBUFFERIZE

//===----------------------------------------------------------------------===//
// TileLoopsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TILELOOPSPASS
struct TileLoopsPassOptions {
  ::llvm::SmallVector<int64_t> tile_sizes_;
  ::llvm::SmallVector<int64_t> unroll_factors_;
};
#undef GEN_PASS_DECL_TILELOOPSPASS
#endif // GEN_PASS_DECL_TILELOOPSPASS
#ifdef GEN_PASS_DEF_TILELOOPSPASS
namespace impl {

template <typename DerivedT>
class TileLoopsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TileLoopsPassBase;

  TileLoopsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TileLoopsPassBase(const TileLoopsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TileLoopsPassBase& operator=(const TileLoopsPassBase &) = delete;
  TileLoopsPassBase(TileLoopsPassBase &&) = delete;
  TileLoopsPassBase& operator=(TileLoopsPassBase &&) = delete;
  ~TileLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tile-loops");
  }
  ::llvm::StringRef getArgument() const override { return "tile-loops"; }

  ::llvm::StringRef getDescription() const override { return "Tiles parallel loops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TileLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "TileLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TileLoopsPassBase<DerivedT>)

  TileLoopsPassBase(TileLoopsPassOptions options) : TileLoopsPassBase() {
    tile_sizes_ = std::move(options.tile_sizes_);
    unroll_factors_ = std::move(options.unroll_factors_);
  }
protected:
  ::mlir::Pass::ListOption<int64_t> tile_sizes_{*this, "tile-sizes", ::llvm::cl::desc("The size of the tile in each dimension, expressed as the number of `unroll_factors_` in that dimension."), llvm::cl::ZeroOrMore};
  ::mlir::Pass::ListOption<int64_t> unroll_factors_{*this, "unroll-factors", ::llvm::cl::desc("The unroll factor in each dimension, expressed as the number of elements in that dimension."), llvm::cl::ZeroOrMore};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TILELOOPSPASS
#endif // GEN_PASS_DEF_TILELOOPSPASS

//===----------------------------------------------------------------------===//
// UnbufferizePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_UNBUFFERIZEPASS
#undef GEN_PASS_DECL_UNBUFFERIZEPASS
#endif // GEN_PASS_DECL_UNBUFFERIZEPASS
#ifdef GEN_PASS_DEF_UNBUFFERIZEPASS
namespace impl {

template <typename DerivedT>
class UnbufferizePassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = UnbufferizePassBase;

  UnbufferizePassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  UnbufferizePassBase(const UnbufferizePassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  UnbufferizePassBase& operator=(const UnbufferizePassBase &) = delete;
  UnbufferizePassBase(UnbufferizePassBase &&) = delete;
  UnbufferizePassBase& operator=(UnbufferizePassBase &&) = delete;
  ~UnbufferizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("unbufferize");
  }
  ::llvm::StringRef getArgument() const override { return "unbufferize"; }

  ::llvm::StringRef getDescription() const override { return "Unbufferize partially bufferized functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UnbufferizePass");
  }
  ::llvm::StringRef getName() const override { return "UnbufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UnbufferizePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_UNBUFFERIZEPASS
#endif // GEN_PASS_DEF_UNBUFFERIZEPASS

//===----------------------------------------------------------------------===//
// VectorizeCopyPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VECTORIZECOPYPASS
#undef GEN_PASS_DECL_VECTORIZECOPYPASS
#endif // GEN_PASS_DECL_VECTORIZECOPYPASS
#ifdef GEN_PASS_DEF_VECTORIZECOPYPASS
namespace impl {

template <typename DerivedT>
class VectorizeCopyPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VectorizeCopyPassBase;

  VectorizeCopyPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorizeCopyPassBase(const VectorizeCopyPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VectorizeCopyPassBase& operator=(const VectorizeCopyPassBase &) = delete;
  VectorizeCopyPassBase(VectorizeCopyPassBase &&) = delete;
  VectorizeCopyPassBase& operator=(VectorizeCopyPassBase &&) = delete;
  ~VectorizeCopyPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vectorize-copy");
  }
  ::llvm::StringRef getArgument() const override { return "vectorize-copy"; }

  ::llvm::StringRef getDescription() const override { return "Pass to vectorize `memref.copy`."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorizeCopyPass");
  }
  ::llvm::StringRef getName() const override { return "VectorizeCopyPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VectorizeCopyPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VECTORIZECOPYPASS
#endif // GEN_PASS_DEF_VECTORIZECOPYPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AllocToArgPass Registration
//===----------------------------------------------------------------------===//

inline void registerAllocToArgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createAllocToArgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAllocToArgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createAllocToArgPass();
  });
}

//===----------------------------------------------------------------------===//
// CollapseParallelLoopsTo1DPass Registration
//===----------------------------------------------------------------------===//

inline void registerCollapseParallelLoopsTo1DPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCollapseParallelLoopsTo1DPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCollapseParallelLoopsTo1DPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCollapseParallelLoopsTo1DPass();
  });
}

//===----------------------------------------------------------------------===//
// ComputeOpAndFuncBufferizePass Registration
//===----------------------------------------------------------------------===//

inline void registerComputeOpAndFuncBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createComputeOpAndFuncBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerComputeOpAndFuncBufferizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createComputeOpAndFuncBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// DetensorizeScfOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerDetensorizeScfOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createDetensorizeScfOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDetensorizeScfOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createDetensorizeScfOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// FinalBufferizePass Registration
//===----------------------------------------------------------------------===//

inline void registerFinalBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFinalBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFinalBufferizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFinalBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// GenericHostToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerGenericHostToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createGenericHostToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGenericHostToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createGenericHostToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerIndexCastPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerIndexCastPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerIndexCastPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerIndexCastPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerIndexCastPass();
  });
}

//===----------------------------------------------------------------------===//
// NaiveCopyRemovalPass Registration
//===----------------------------------------------------------------------===//

inline void registerNaiveCopyRemovalPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createNaiveCopyRemovalPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerNaiveCopyRemovalPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createNaiveCopyRemovalPass();
  });
}

//===----------------------------------------------------------------------===//
// OneShotBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerOneShotBufferize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createOneShotBufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOneShotBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createOneShotBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// TileLoopsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTileLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTileLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTileLoopsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTileLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// UnbufferizePass Registration
//===----------------------------------------------------------------------===//

inline void registerUnbufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createUnbufferizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerUnbufferizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return hlo::createUnbufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// VectorizeCopyPass Registration
//===----------------------------------------------------------------------===//

inline void registerVectorizeCopyPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createVectorizeCopyPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVectorizeCopyPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createVectorizeCopyPass();
  });
}

//===----------------------------------------------------------------------===//
// LMHLOTransforms Registration
//===----------------------------------------------------------------------===//

inline void registerLMHLOTransformsPasses() {
  registerAllocToArgPass();
  registerCollapseParallelLoopsTo1DPass();
  registerComputeOpAndFuncBufferizePass();
  registerDetensorizeScfOpsPass();
  registerFinalBufferizePass();
  registerGenericHostToLLVMPass();
  registerLowerIndexCastPass();
  registerNaiveCopyRemovalPass();
  registerOneShotBufferize();
  registerTileLoopsPass();
  registerUnbufferizePass();
  registerVectorizeCopyPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AllocToArgPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = AllocToArgPassBase;

  AllocToArgPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AllocToArgPassBase(const AllocToArgPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  AllocToArgPassBase& operator=(const AllocToArgPassBase &) = delete;
  AllocToArgPassBase(AllocToArgPassBase &&) = delete;
  AllocToArgPassBase& operator=(AllocToArgPassBase &&) = delete;
  ~AllocToArgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("alloc-to-arg");
  }
  ::llvm::StringRef getArgument() const override { return "alloc-to-arg"; }

  ::llvm::StringRef getDescription() const override { return "Hoist memref allocations to function arguments."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AllocToArgPass");
  }
  ::llvm::StringRef getName() const override { return "AllocToArgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AllocToArgPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class CollapseParallelLoopsTo1DPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CollapseParallelLoopsTo1DPassBase;

  CollapseParallelLoopsTo1DPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CollapseParallelLoopsTo1DPassBase(const CollapseParallelLoopsTo1DPassBase &other) : ::mlir::OperationPass<>(other) {}
  CollapseParallelLoopsTo1DPassBase& operator=(const CollapseParallelLoopsTo1DPassBase &) = delete;
  CollapseParallelLoopsTo1DPassBase(CollapseParallelLoopsTo1DPassBase &&) = delete;
  CollapseParallelLoopsTo1DPassBase& operator=(CollapseParallelLoopsTo1DPassBase &&) = delete;
  ~CollapseParallelLoopsTo1DPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("collapse-parallel-loops-to-1d");
  }
  ::llvm::StringRef getArgument() const override { return "collapse-parallel-loops-to-1d"; }

  ::llvm::StringRef getDescription() const override { return "Collapses multidimensional loops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CollapseParallelLoopsTo1DPass");
  }
  ::llvm::StringRef getName() const override { return "CollapseParallelLoopsTo1DPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CollapseParallelLoopsTo1DPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ComputeOpAndFuncBufferizePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ComputeOpAndFuncBufferizePassBase;

  ComputeOpAndFuncBufferizePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ComputeOpAndFuncBufferizePassBase(const ComputeOpAndFuncBufferizePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ComputeOpAndFuncBufferizePassBase& operator=(const ComputeOpAndFuncBufferizePassBase &) = delete;
  ComputeOpAndFuncBufferizePassBase(ComputeOpAndFuncBufferizePassBase &&) = delete;
  ComputeOpAndFuncBufferizePassBase& operator=(ComputeOpAndFuncBufferizePassBase &&) = delete;
  ~ComputeOpAndFuncBufferizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("computeop-and-func-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "computeop-and-func-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform compute operations (hlo and linalg) on values to buffer based ones."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ComputeOpAndFuncBufferizePass");
  }
  ::llvm::StringRef getName() const override { return "ComputeOpAndFuncBufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ComputeOpAndFuncBufferizePassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DetensorizeScfOpsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = DetensorizeScfOpsPassBase;

  DetensorizeScfOpsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DetensorizeScfOpsPassBase(const DetensorizeScfOpsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  DetensorizeScfOpsPassBase& operator=(const DetensorizeScfOpsPassBase &) = delete;
  DetensorizeScfOpsPassBase(DetensorizeScfOpsPassBase &&) = delete;
  DetensorizeScfOpsPassBase& operator=(DetensorizeScfOpsPassBase &&) = delete;
  ~DetensorizeScfOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("detensorize-scf-ops");
  }
  ::llvm::StringRef getArgument() const override { return "detensorize-scf-ops"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize arguments of SCF ops where possible."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DetensorizeScfOpsPass");
  }
  ::llvm::StringRef getName() const override { return "DetensorizeScfOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DetensorizeScfOpsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FinalBufferizePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FinalBufferizePassBase;

  FinalBufferizePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalBufferizePassBase(const FinalBufferizePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FinalBufferizePassBase& operator=(const FinalBufferizePassBase &) = delete;
  FinalBufferizePassBase(FinalBufferizePassBase &&) = delete;
  FinalBufferizePassBase& operator=(FinalBufferizePassBase &&) = delete;
  ~FinalBufferizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("final-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "final-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform late operations on values to buffer based ones."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalBufferizePass");
  }
  ::llvm::StringRef getName() const override { return "FinalBufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalBufferizePassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<uint64_t> alignment_{*this, "alignment", ::llvm::cl::desc("Memory alignment"), ::llvm::cl::init(64)};
};

template <typename DerivedT>
class GenericHostToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GenericHostToLLVMPassBase;

  GenericHostToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GenericHostToLLVMPassBase(const GenericHostToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  GenericHostToLLVMPassBase& operator=(const GenericHostToLLVMPassBase &) = delete;
  GenericHostToLLVMPassBase(GenericHostToLLVMPassBase &&) = delete;
  GenericHostToLLVMPassBase& operator=(GenericHostToLLVMPassBase &&) = delete;
  ~GenericHostToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("generic-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "generic-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Pass to lower common dialects resulting from HLO to LLVM."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GenericHostToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "GenericHostToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::LLVM::LLVMDialect>();
    registry.insert<::mlir::arith::ArithDialect>();
    registry.insert<::mlir::cf::ControlFlowDialect>();
    registry.insert<::mlir::complex::ComplexDialect>();
    registry.insert<::mlir::func::FuncDialect>();
    registry.insert<::mlir::math::MathDialect>();
    registry.insert<::mlir::memref::MemRefDialect>();
    registry.insert<::mlir::scf::SCFDialect>();
    registry.insert<::mlir::x86vector::X86VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GenericHostToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enableAvx2{*this, "enable-avx2", ::llvm::cl::desc("Whether to enable avx2"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LowerIndexCastPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LowerIndexCastPassBase;

  LowerIndexCastPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerIndexCastPassBase(const LowerIndexCastPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  LowerIndexCastPassBase& operator=(const LowerIndexCastPassBase &) = delete;
  LowerIndexCastPassBase(LowerIndexCastPassBase &&) = delete;
  LowerIndexCastPassBase& operator=(LowerIndexCastPassBase &&) = delete;
  ~LowerIndexCastPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-index-cast");
  }
  ::llvm::StringRef getArgument() const override { return "lower-index-cast"; }

  ::llvm::StringRef getDescription() const override { return "Lower index cast on tensors to tensor dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerIndexCastPass");
  }
  ::llvm::StringRef getName() const override { return "LowerIndexCastPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerIndexCastPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class NaiveCopyRemovalPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = NaiveCopyRemovalPassBase;

  NaiveCopyRemovalPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  NaiveCopyRemovalPassBase(const NaiveCopyRemovalPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  NaiveCopyRemovalPassBase& operator=(const NaiveCopyRemovalPassBase &) = delete;
  NaiveCopyRemovalPassBase(NaiveCopyRemovalPassBase &&) = delete;
  NaiveCopyRemovalPassBase& operator=(NaiveCopyRemovalPassBase &&) = delete;
  ~NaiveCopyRemovalPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("naive-copy-removal");
  }
  ::llvm::StringRef getArgument() const override { return "naive-copy-removal"; }

  ::llvm::StringRef getDescription() const override { return "Pass to remove redundant `memref.copy` ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NaiveCopyRemovalPass");
  }
  ::llvm::StringRef getName() const override { return "NaiveCopyRemovalPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NaiveCopyRemovalPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class OneShotBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OneShotBufferizeBase;

  OneShotBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OneShotBufferizeBase(const OneShotBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  OneShotBufferizeBase& operator=(const OneShotBufferizeBase &) = delete;
  OneShotBufferizeBase(OneShotBufferizeBase &&) = delete;
  OneShotBufferizeBase& operator=(OneShotBufferizeBase &&) = delete;
  ~OneShotBufferizeBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-one-shot-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-one-shot-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "One shot bufferization pass."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OneShotBufferize");
  }
  ::llvm::StringRef getName() const override { return "OneShotBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OneShotBufferizeBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TileLoopsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TileLoopsPassBase;

  TileLoopsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TileLoopsPassBase(const TileLoopsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TileLoopsPassBase& operator=(const TileLoopsPassBase &) = delete;
  TileLoopsPassBase(TileLoopsPassBase &&) = delete;
  TileLoopsPassBase& operator=(TileLoopsPassBase &&) = delete;
  ~TileLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tile-loops");
  }
  ::llvm::StringRef getArgument() const override { return "tile-loops"; }

  ::llvm::StringRef getDescription() const override { return "Tiles parallel loops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TileLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "TileLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TileLoopsPassBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<int64_t> tile_sizes_{*this, "tile-sizes", ::llvm::cl::desc("The size of the tile in each dimension, expressed as the number of `unroll_factors_` in that dimension."), llvm::cl::ZeroOrMore};
  ::mlir::Pass::ListOption<int64_t> unroll_factors_{*this, "unroll-factors", ::llvm::cl::desc("The unroll factor in each dimension, expressed as the number of elements in that dimension."), llvm::cl::ZeroOrMore};
};

template <typename DerivedT>
class UnbufferizePassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = UnbufferizePassBase;

  UnbufferizePassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  UnbufferizePassBase(const UnbufferizePassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  UnbufferizePassBase& operator=(const UnbufferizePassBase &) = delete;
  UnbufferizePassBase(UnbufferizePassBase &&) = delete;
  UnbufferizePassBase& operator=(UnbufferizePassBase &&) = delete;
  ~UnbufferizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("unbufferize");
  }
  ::llvm::StringRef getArgument() const override { return "unbufferize"; }

  ::llvm::StringRef getDescription() const override { return "Unbufferize partially bufferized functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UnbufferizePass");
  }
  ::llvm::StringRef getName() const override { return "UnbufferizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UnbufferizePassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VectorizeCopyPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VectorizeCopyPassBase;

  VectorizeCopyPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorizeCopyPassBase(const VectorizeCopyPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VectorizeCopyPassBase& operator=(const VectorizeCopyPassBase &) = delete;
  VectorizeCopyPassBase(VectorizeCopyPassBase &&) = delete;
  VectorizeCopyPassBase& operator=(VectorizeCopyPassBase &&) = delete;
  ~VectorizeCopyPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vectorize-copy");
  }
  ::llvm::StringRef getArgument() const override { return "vectorize-copy"; }

  ::llvm::StringRef getDescription() const override { return "Pass to vectorize `memref.copy`."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorizeCopyPass");
  }
  ::llvm::StringRef getName() const override { return "VectorizeCopyPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VectorizeCopyPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
