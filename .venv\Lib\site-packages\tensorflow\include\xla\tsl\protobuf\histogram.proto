syntax = "proto3";

package tensorflow;

option cc_enable_arenas = true;
option java_multiple_files = true;
option java_package = "org.tensorflow.framework";
option go_package = "github.com/google/tsl/tsl/go/core/protobuf/summary_go_proto";

// Serialization format for histogram module in
// tsl/lib/histogram/histogram.h
message HistogramProto {
  double min = 1;
  double max = 2;
  double num = 3;
  double sum = 4;
  double sum_squares = 5;

  // Parallel arrays encoding the bucket boundaries and the bucket values.
  // bucket(i) is the count for the bucket i.  The range for
  // a bucket is:
  //   i == 0:  -DBL_MAX .. bucket_limit(0)
  //   i != 0:  bucket_limit(i-1) .. bucket_limit(i)
  repeated double bucket_limit = 6 [packed = true];
  repeated double bucket = 7 [packed = true];
}
